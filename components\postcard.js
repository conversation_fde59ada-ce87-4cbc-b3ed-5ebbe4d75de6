import React from "react"
import Link from "next/link"
import Image from "next/image"
import Moment from "react-moment"
import { getStrapiMedia } from "../lib/media"

const Postcard = ({ post }) => {
  return (
    <div className="col-4">
        <Link href={`/blog/${post.attributes.slug}`}>
            <a className="">
                <div className="blog-group-item">
                    <div className="img-holder">
                        {post.attributes.image?.data && (
                            <div style={{display: "inline-block", maxWidth: "100%", overflow: "hidden", position: "relative", boxSizing: "border-box", margin: 0}}>
                                <div style={{boxSizing: "border-box", display: "block", maxWidth: "100%"}}>
                                    <img 
                                        alt="" 
                                        aria-hidden="true" 
                                        src={`data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iJHtwb3N0LmF0dHJpYnV0ZXMuaW1hZ2UuZGF0YS5hdHRyaWJ1dGVzLndpZHRofSIgaGVpZ2h0PSIke3Bvc3QuYXR0cmlidXRlcy5pbWFnZS5kYXRhLmF0dHJpYnV0ZXMuaGVpZ2h0fSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2ZXJzaW9uPSIxLjEiLz4=`} 
                                        style={{maxWidth: "100%", display: "block", margin: 0, border: "none", padding: 0}} 
                                    />
                                </div>
                                <Image 
                                    src={getStrapiMedia(post.attributes.image)} 
                                    alt={`blog group image ${post.attributes.title}`} 
                                    width={post.attributes.image.data.attributes.width} 
                                    height={post.attributes.image.data.attributes.height} 
                                    className="img-fluid" 
                                    style={{
                                        position: "absolute", 
                                        inset: 0, 
                                        boxSizing: "border-box", 
                                        padding: 0, 
                                        border: "none", 
                                        margin: "auto", 
                                        display: "block", 
                                        width: 0, 
                                        height: 0, 
                                        minWidth: "100%", 
                                        maxWidth: "100%", 
                                        minHeight: "100%", 
                                        maxHeight: "100%"
                                    }}
                                />
                            </div>
                        )}
                    </div>
                    <div className="txt-holder">
                        <h3>{post.attributes.title}</h3>
                        <h5><Moment format="MM/DD/YYYY">{post.attributes.date}</Moment></h5>
                        <p>{post.attributes.description}</p>
                        <span className="btn link-btn underline-btn">Read Article →</span>
                    </div>
                </div>
            </a>
        </Link>
    </div>
  )
}

export default Postcard
