import React from "react"
import Image from "next/image"
import Link from "next/link"
import Moment from "react-moment"
import { getStrapiMedia } from "../lib/media"

const Learncard = ({ post }) => {
  return (
    <Link href={`/videos/${post.attributes.slug}`} className="col-4" style={{cursor: "pointer"}}>
        <div className="blog-group-item">
            <div className="img-holder">
                <Image src={getStrapiMedia(post.attributes.image)} className="img-fluid" alt="learn image ethos tracking" style={{"width" : "100%"}} width={post.attributes.image.data.attributes.width} height={post.attributes.image.data.attributes.height} />
                <div className="play-holder">
                    <span><Image src="/images/play-video-icon.svg" alt="play ethos tracking" width="112" height="112" className="img-fluid" /></span>
                </div>
            </div>
            <div className="txt-holder">
                <h3>{post.attributes.title}</h3>
            </div>
        </div>
    </Link>
  )
}

export default Learncard
