// This script is used to create a minimal Next.js build for Netlify
const fs = require('fs');
const { execSync } = require('child_process');

console.log('Starting Netlify-specific build process...');

// Create a .env.local file with the necessary environment variables
const envContent = `
NEXT_PUBLIC_GA_ID=G-4TXP8N8D63
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-4TXP8N8D63
NEXT_PUBLIC_STRAPI_API_URL=https://phpstack-696462-2348429.cloudwaysapps.com
NETLIFY=true
CONTEXT=production
`;

fs.writeFileSync('.env.local', envContent);
console.log('Created .env.local file');

// Run the Next.js build
try {
  console.log('Running Next.js build...');
  execSync('next build', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NETLIFY: 'true',
      CONTEXT: 'production',
      NEXT_PUBLIC_GA_ID: 'G-4TXP8N8D63',
      NEXT_PUBLIC_GA_MEASUREMENT_ID: 'G-4TXP8N8D63',
      NEXT_PUBLIC_STRAPI_API_URL: 'https://phpstack-696462-2348429.cloudwaysapps.com'
    }
  });
  console.log('Build completed successfully');
} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
}
