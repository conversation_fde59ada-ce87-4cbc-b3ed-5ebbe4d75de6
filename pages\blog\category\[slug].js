import React from "react"
import Postcard from "../../../components/postcard"
import { fetchAPI } from "../../../lib/api"
import Layout from "../../../components/layout"
import Seo from "../../../components/seo"
import Categories from "../../../components/categories"
import Search from "../../../components/search"
import Link from "next/link"
import Image from "next/image"
import { getStrapiMedia } from "../../../lib/media"

const Category = ({ category, categories }) => {
  // Add safety check for category data
  if (!category || !category.attributes) {
    return (
      <Layout categories={categories?.data || []}>
        <main>
          <section className="blog-full-section pt-5">
            <div className="container mw-1000 blog-full-container">
              <div className="row blog-full-content">
                <div className="col-12 heading-content mb-100">
                  <h1>Category not found</h1>
                </div>
                <div className="col-12 blog-full-item">
                  <div className="btn-holder">
                    <Link href="/blog"><a className="btn border-btn fs-14">BACK TO BLOG</a></Link>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
      </Layout>
    );
  }

  const seo = {
    metaTitle: category.attributes.name,
    metaDescription: `All ${category.attributes.name} posts`,
  }
  
  const sorted_posts = category.attributes.posts?.data || [];

  return (
    <Layout categories={categories?.data || []}>
    <Seo seo={seo} />
        <main>
            <section className="main-img-section">
                <div className="container-fluid main-img-container">
                    <div className="row main-img-content">
                        <div className="col-12 main-img-item">
                            <div className="bg-holder">
                            {category.attributes.image?.data && (
                                <div style={{display: "inline-block", maxWidth: "100%", overflow: "hidden", position: "relative", boxSizing: "border-box", margin: 0}}>
                                    <div style={{boxSizing: "border-box", display: "block", maxWidth: "100%"}}>
                                        <img alt="" aria-hidden="true" src={`data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iJHtjYXRlZ29yeS5hdHRyaWJ1dGVzLmltYWdlLmRhdGEuYXR0cmlidXRlcy53aWR0aCB8fCAxMjAwfSIgaGVpZ2h0PSIke2NhdGVnb3J5LmF0dHJpYnV0ZXMuaW1hZ2UuZGF0YS5hdHRyaWJ1dGVzLmhlaWdodCB8fCA4MDB9IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIvPg==`} style={{maxWidth: "100%", display: "block", margin: 0, border: "none", padding: 0}} />
                                    </div>
                                    <Image 
                                        src={getStrapiMedia(category.attributes.image)} 
                                        alt={category.attributes.name || "Category"} 
                                        className="img-fluid" 
                                        width={category.attributes.image.data.attributes.width || 1200} 
                                        height={category.attributes.image.data.attributes.height || 800} 
                                        style={{
                                            position: "absolute", 
                                            inset: 0, 
                                            boxSizing: "border-box", 
                                            padding: 0, 
                                            border: "none", 
                                            margin: "auto", 
                                            display: "block", 
                                            width: 0, 
                                            height: 0, 
                                            minWidth: "100%", 
                                            maxWidth: "100%", 
                                            minHeight: "100%", 
                                            maxHeight: "100%"
                                        }}
                                    />
                                </div>
                            )}
                            </div>
                            <div className="heading-holder">
                                <div className="container">
                                    <h1>{category.attributes.name}</h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section className="main-blog-section">
                <div className="container filter-container mb-100">
                    <div className="row filter-content">
                        <div className="col-6 filter-heading">
                            <h2>Read. Get Inspired.</h2>
                        </div>
                        <ul className="col-6 filter-items">
                            <li className="filter-item">
                                <Link href={"/blog"}><a className="btn border-btn">ALL</a></Link>
                            </li>
                            {(categories?.data || []).map((cat, i) => {
                                return (
                                    <Categories
                                        category={cat}
                                        current={category.attributes.slug}
                                        type={'blog'}
                                        key={`blog_categories___${i}`}
                                    />
                                )
                            })}
                            <Search type={'blog'} />
                        </ul>
                    </div>
                </div>
                <div className="container blog-group-container mb-150">
                    <div className="row blog-group-content">
                        {sorted_posts.length > 0 ? (
                            sorted_posts.map((post, i) => {
                                return (
                                    <Postcard
                                        post={post}
                                        key={`blog_categories___${i}`}
                                    />
                                )
                            })
                        ) : (
                            <div className="col-12 text-center">
                                <p>No posts found in this category.</p>
                            </div>
                        )}
                    </div>
                </div>
            </section>
        </main>
    </Layout>
  )
}

export async function getStaticPaths() {
  try {
    const categoriesRes = await fetchAPI("/categories", { fields: ["slug"] });
    
    return {
      paths: categoriesRes.data.map((category) => ({
        params: {
          slug: category.attributes.slug,
        },
      })),
      fallback: 'blocking',
    };
  } catch (error) {
    console.error("Error fetching categories for static paths:", error);
    return {
      paths: [],
      fallback: 'blocking',
    };
  }
}

export async function getStaticProps({ params }) {
  try {
    const [categoryRes, categoriesRes] = await Promise.all([
      fetchAPI("/categories", {
        filters: { slug: params.slug },
        populate: {
          image: "*",
          posts: {
            populate: "*",
          },
        },
      }),
      fetchAPI("/categories", { populate: "*" }),
    ]);

    // Safety check - if no data or empty data, return fallback
    if (!categoryRes || !categoryRes.data || categoryRes.data.length === 0) {
      return {
        notFound: true, // This will show a 404 page
      };
    }

    return {
      props: {
        category: categoryRes.data[0],
        categories: categoriesRes,
      },
      revalidate: 1,
    };
  } catch (error) {
    console.error("Error fetching data for category page:", error);
    return {
      notFound: true, // This will show a 404 page
    };
  }
}

export default Category
