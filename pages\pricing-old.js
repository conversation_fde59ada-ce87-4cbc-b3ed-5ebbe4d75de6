import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import { fetchAPI } from "../lib/api"
import Image from 'next/image'
import Link from 'next/link'
import { getStrapiMedia } from "../lib/media"

const Pricing = ({ pages, plans }) => {
  return (
    <Layout pages={pages}>
        <Seo seo={pages.attributes.seo} />
        <main>
            <section className="main-img-section">
                <div className="container-fluid main-img-container">
                    <div className="row main-img-content">
                        <div className="col-12 main-img-item">
                            <div className="bg-holder">
                                <Image src={getStrapiMedia(pages.attributes.headerimage)} width="1920" height="300" alt="Ethos" className="img-fluid" />
                            </div>
                            <div className="heading-holder">
                                <div className="container">
                                    <h1>{pages.attributes.title}</h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <div className="bg-texture">
                <section className="pricing-list-main-section">
                    <div className="container pricing-list-main-container">
                        {/* <div className="row">
                            <div className="col-12 txt-holder text-center heading-holder">
                                <h5>PRICING PLANS</h5>
                                <h3>Choose Your Pricing Plan</h3>
                            </div>
                        </div> */}
                        {/* <div className="row pricing-content">
                            {(plans).map((plan, i) => {
                                return (
                                    <div className="col-4" key={`single_plan__` + i}>
                                        <div className="pricing-item">
                                            <h3>{plan.attributes.title}</h3>
                                            <h5>{plan.attributes.users}</h5>
                                            <div className="price-container">
                                                <span>${plan.attributes.price}<small>/mo</small></span>
                                                {
                                                    plan.attributes.best ?
                                                    <div className="badge-holder">
                                                        <Image src="/images/circle-icon.svg" alt="" width={60} height={60} className="img-fluid" />
                                                        <span>BEST VALUE</span>
                                                    </div>
                                                    :
                                                    ""
                                                }
                                            </div>
                                            <ul>
                                            {(plan.attributes.list).map((singleList, i) => {
                                                const isTitleBold = singleList.isTitle !== null ? <strong>{singleList.title}</strong> : singleList.title
                                                return (
                                                    <li key={'single_plan_title_' + i}>
                                                        {isTitleBold}
                                                    </li>
                                                    )
                                                })}
                                            </ul>
                                            <div className="btn-holder">
                                                <Link href="/contact-us"><a className={"btn " + (plan.attributes.best ? "second-bg white box-shadow" : "border-btn")}>GET STARTED</a></Link>
                                            </div>
                                        </div>
                                    </div>
                                )
                            })}
                        </div> */}
                        <div className="row pricing-content">
                            <div className="col-12">
                                <div className="pricing-item text-center w1000">
                                    <h1 className="red f-48 mb-2">Get Started Today! </h1>
                                    <h3 className="f-22 mb-3">Contact us for pricing</h3>
                                    <h4 className="f-18 mb-4">To set up an Ethos Tracking demo, or for additional questions please reach out.</h4>
                                    <Link href="/contact-us"><a className="btn second-bg white box-shadow">GET STARTED</a></Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section className="mt-5 pt-5">
                    <div className="container benefits-container mw-900">
                        {(pages.attributes.sections).map((section, i) => {
                            return (
                                <div className="row" key={'single_sect_title_' + i}>
                                    <div className="col-12 heading-holder txt-holder mb-0">
                                        {/* <h5>{section.smalltitle}</h5> */}
                                        {/* <h3 className="text-center">{section.title}</h3> */}
                                        {/* <p>{section.description}</p> */}
                                        <div className="row features-main-content">
                                            <div className="features-main-text">
                                                <div dangerouslySetInnerHTML={{ __html: pages.attributes.content }} />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )
                        })}
                        {/* <ul className="row benefits-content">
                            {(pages.attributes.features).map((feature, i) => {
                                if(feature.isTitle){
                                    return (
                                        <li className="col-12 benefits-item">
                                            <h3>{feature.title}</h3>
                                        </li>
                                    )
                                }else{
                                    return (
                                        <li className="col-12 benefits-item">
                                            <p>{feature.title}</p>
                                            <Image src="/images/checked-icon.svg" alt="Checked" width={20} height={20} className="img-fluid" />
                                        </li>
                                    )
                                }
                            })}
                        </ul> */}
                    </div>
                </section>

                <section className="pricing-feature-section">
                    <div className="container pricing-feature-container">
                        <div className="row pricing-feature-content">
                            <Link href="features">
                                <a className="col-6 pricing-feature-item">
                                    <div className="icon-holder">
                                        <Image src="/images/save-money-icon.svg" alt="Pricing" width={31} height={31} className="img-fluid" />
                                    </div>
                                    <div className="txt-holder">
                                        <h3>View Features →</h3>
                                        {/* <p>Aliquam interdum risus vitae lectus convallis sodales<br /> quis dignissim mi.</p> */}
                                    </div>
                                </a>
                            </Link>
                            <Link href="contact-us">
                                <a className="col-12 pricing-feature-item offset-5 col-7">
                                    <div className="icon-holder">
                                        <Image src="/images/question-icon.svg" alt="Pricing" width={17} height={30} className="img-fluid" />
                                    </div>
                                    <div className="txt-holder">
                                        <h3>Contact Sales →</h3>
                                        {/* <p>Aliquam interdum risus vitae lectus convallis sodales<br /> quis dignissim mi.</p> */}
                                    </div>
                                </a>
                            </Link>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </Layout>
  )
}


export async function getServerSideProps() {
    try {
        const pagesRes = await fetchAPI("/pages/12", {
            populate: {
                sections: {
                    populate: {
                        image: "*"
                    }
                },
                features: {
                    populate: {
                        image: "*"
                    }
                },
                image: {
                    populate: "*"
                },
                headerimage: {
                    populate: "*"
                },
                seo: {
                    populate: "*"
                }
            }
        })
        const plansRes = await fetchAPI("/pricing-plans", {
            populate: {
                list: "*"
            }
        })

        return {
            props: {
                plans: plansRes.data || [],
                pages: pagesRes.data || null,
            }
        }
    } catch (error) {
        console.error("Error fetching pricing-old page data:", error);
        return {
            props: {
                plans: [],
                pages: null,
            }
        }
    }
}

export default Pricing
