import React from "react"
import Postcard from "../components/postcard"
import Categories from "../components/categories"
import Layout from "../components/layout"
import Seo from "../components/seo"
import Search from "../components/search"
import Link from "next/link"
import Image from "next/image"
import { fetchAPI } from "../lib/api"
import { getStrapiMedia } from "../lib/media"

const Blog = ({ posts, categories, blogpage }) => {
    const sortPostsByDate = (posts) => {
        return posts.sort((a, b) => {
          const aDate = new Date(a.attributes.publishedAt);
          const bDate = new Date(b.attributes.publishedAt);
          return bDate - aDate;
        });
    };
    // const sorted_posts = sortPostsByDate(posts);
    const sorted_posts = posts;

    return (
        <Layout>
            <Seo seo={blogpage.attributes.seo} />
            <main>
                <section className="main-img-section">
                    <div className="container-fluid main-img-container">
                        <div className="row main-img-content">
                            <div className="col-12 main-img-item">
                                <div className="bg-holder">
                                    <Image src={getStrapiMedia(blogpage.attributes.headerimage)} width={blogpage.attributes.headerimage.data.attributes.width} height={blogpage.attributes.headerimage.data.attributes.height} className="img-fluid" alt="blog header image ethos tracking" />
                                </div>
                                <div className="heading-holder">
                                    <div className="container">
                                        <h1>{blogpage.attributes.title}</h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section className="main-blog-section">
                    <div className="container filter-container mb-100">
                        <div className="row filter-content">
                            <div className="col-6 filter-heading">
                                <h2>Read. Get Inspired.</h2>
                            </div>
                            <ul className="col-6 filter-items blog_categories">
                                <li className="filter-item">
                                    <Link href={"/blog"}><a className="active btn white second-bg box-shadow">ALL</a></Link>
                                </li>
                                {categories.map((category, i) => {
                                    return (
                                        <Categories
                                            category={category}
                                            type={'blog'}
                                            key={`blog_single_category__${i}`}
                                        />
                                    )
                                })}
                                <Search type={'blog'} />
                            </ul>
                        </div>
                    </div>
                    <div className="container blog-group-container mb-150">
                        <div className="row blog-group-content">
                            {sorted_posts.map((post, i) => {
                                return (
                                    <Postcard
                                        post={post}
                                        key={`blog_posts__${i}`}
                                    />
                                )
                            })}
                        </div>
                    </div>
                </section>
            </main>
        </Layout>
    )
}

export async function getServerSideProps() {
    try {
        // Run API calls in parallel
        const [postsRes, categoriesRes, blogRes] = await Promise.all([
            fetchAPI("/posts", {
                populate: "*",
                pagination: {
                    pageSize: 100
                },
                sort: 'date:desc'
            }),
            fetchAPI("/categories", { populate: "*" }),
            fetchAPI("/pages/3", { populate: "*" }),
        ])
        return {
            props: {
                posts: postsRes.data || [],
                categories: categoriesRes.data || [],
                blogpage: blogRes.data || null
            }
        }
    } catch (error) {
        console.error("Error fetching blog data:", error);
        return {
            props: {
                posts: [],
                categories: [],
                blogpage: null
            }
        }
    }
}

export default Blog
