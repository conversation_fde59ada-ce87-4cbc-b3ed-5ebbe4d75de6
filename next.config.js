/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ["phpstack-1462486-5579835.cloudwaysapps.com"],
    unoptimized: true,
  },
  serverRuntimeConfig: {
    mySecret: 'secret',
  },
  publicRuntimeConfig: {
    baseUrl: 'https://www.ethostracking.com',
  },
  generateBuildId: async () => {
    return process.env.COMMIT_REF || 'development'
  },
  // For Netlify deployment
  trailingSlash: true,
  // Add this to ignore ESLint errors during build
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Increase memory limit for builds
  experimental: {
    largePageDataBytes: 400 * 1000, // Increased from 128KB to 400KB
  },
  // Disable strict mode for now to avoid double renders
  reactStrictMode: false,
  // Webpack configuration for better builds
  webpack: (config, { isServer }) => {
    // Optimize for production builds
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }
    return config;
  },
}

module.exports = nextConfig
