@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400&display=swap');

/* FOR LOADER ON BUTTONS - ajax */
.bg--loading {
	position: relative;
}
.products_list.bg--loading::before {
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: rgba(255, 255, 255, 0.85);
    z-index: 1;
}
.bottom-info.white ul li::before {
	border-color: white;
}
.bg--loading::after {
		content: '';
		display: block;
		width: 44px;
		height: 44px;
		position: absolute;
		left: 50%;
		top: 50%;
        z-index: 2;
		margin-left: -12px;
		margin-top: -12px;
		border-radius: 50%;
		border: 5px solid #000;
		border-top-color: transparent;
		-webkit-animation: spin 1s infinite linear;
		-moz-animation: spin 1s infinite linear;
		-o-animation: spin 1s infinite linear;
		animation: spin 1s infinite linear;
}
body .btn--loading {
		color: rgba(0,0,0,0) !important;
		transition: none !important;
		position: relative;
}
body .btn--loading::after {
		content: '';
		display: block;
		width: 18px;
		height: 18px;
		position: absolute;
		left: 50%;
		top: 50%;
		margin-left: -12px;
		margin-top: -12px;
		border-radius: 50%;
		border: 3px solid #fff;
		border-top-color: transparent;
		-webkit-animation: spin 1s infinite linear;
		-moz-animation: spin 1s infinite linear;
		-o-animation: spin 1s infinite linear;
		animation: spin 1s infinite linear;
}
@-webkit-keyframes spin{
		0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}
		100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}
}
@-moz-keyframes spin{
		0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}
		100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}
}
@-ms-keyframes spin{
		0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}
		100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}
}
@keyframes spin{
		0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}
		100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}
}
.msg-popup {
	position: fixed;
	top: 80px;
	left: 50%;
	width: 100%;
	max-width: 38vw;
	z-index: 111111111111;
	padding: 30px;
	color: #000;
	background: #fff;
	border-radius: 0px;
	box-shadow: 0px 0px 50px rgba(51, 51, 51, 0.1);
	transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;
	opacity: 0;
	pointer-events: none;
    visibility: hidden;
	transform: translateX(-50%);
}
.msg-popup.danger { background: #ff5757 }
.msg-popup.danger p { color: #fff }
.msg-popup.danger .close_popup { color: #fff }
.msg-popup.warning { background: #ffc557; }
.msg-popup.warning p { color: #000; }
.msg-popup.warning .close_popup { color: #000; }
.msg-popup.info { background: #34bafb; }
.msg-popup.info p { color: #000; }
.msg-popup.success { background: rgb(19, 170, 87); }
.msg-popup.success p{ color: #fff; }
.msg-popup.success .close_popup { color: #fff; }

.close_popup {
    position: absolute;
    top: 28px;
    right: 25px;
    padding: 0;
    line-height: 1;
    font-size: 40px;
    font-weight: 400;
    cursor: pointer;
}
.close_popup:hover {
    filter: invert(1);
}
.msg-popup.showw {
    pointer-events: auto;
    visibility: visible;
    top: 140px;
    opacity: 1;
}
.msg-popup p {
	color: #000;
	text-align: left;
	margin: 0;
	font-size: 16px;
	font-weight: 400;
}
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, main, nav, output, ruby, section, summary,
time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    vertical-align: baseline;
    box-sizing: border-box;
    font-family: "Lato", sans-serif;
  }
  button {
    border: none;
    cursor: pointer;
  }
  input, textarea, select {
    display: block;
    box-sizing: border-box;
    letter-spacing: 0.05em;
    font-family: "Lato", sans-serif;
}
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}
ul {
  list-style: none;
}
ol {
  padding-left: 40px;
}
blockquote, q {
  quotes: none;
}
a {
    color: #000;
    text-decoration: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
  content: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
th {
    color: #fff;
}
th, td {
    padding: 13px;
}
td {
    border: 2px solid #D4D8DD;
    background: #fff;
}
/* CHECKBOX */
[type="checkbox"]:not(:checked),
[type="checkbox"]:checked {
  position: absolute;
  left: -9999px;
}
[type="checkbox"]:not(:checked) + label,
[type="checkbox"]:checked + label {
    cursor: pointer;
    font-size: 16px;
    padding-left: 45px;
    position: relative;
    user-select: none;
    display: block;
}
.form-options [type="checkbox"]:not(:checked) + label,
.form-options [type="checkbox"]:checked + label {
    font-size: 12px;
    color: #A7AEB9;
}
.dark-form [type="checkbox"]:not(:checked) + label:before,
.dark-form [type="checkbox"]:checked + label:before,
.dark-form [type="checkbox"]:not(:checked) + label:after,
.dark-form [type="checkbox"]:checked + label:after {
    background: none repeat scroll 0 0 rgba(0,0,0,0);
}
[type="checkbox"]:not(:checked) + label::before, [type="checkbox"]:checked + label::before {
	background: none repeat scroll 0 0 #fff;
	border: 1px solid #e5e5e5;
	border-radius: 3px;
	content: "";
	height: 20px;
	left: 0;
	position: absolute;
	top: -2px;
	width: 20px;
}
[type="checkbox"]:not(:checked) + label::after, [type="checkbox"]:checked + label::after {
	background: none repeat scroll 0 0 #333;
	content: "";
	font-size: 16px;
	height: 20px;
	left: 1px;
	margin-top: 0px;
	position: absolute;
	top: -1px;
	transition: all 0.2s ease 0s;
	width: 20px;
	border-radius: 3px;
}
[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  transform: scale(0);
}
[type="checkbox"]:checked + label:after {
  opacity: 1;
  transform: scale(1);
}
[type="checkbox"]:disabled:not(:checked) + label:before,
[type="checkbox"]:disabled:checked + label:before {
  box-shadow: none;
  border-color: #bbb;
  background-color: #ddd;
}
[type="checkbox"]:disabled:checked + label:after {
  color: #999;
}
[type="checkbox"]:disabled + label {
  color: #aaa;
}

/* RADIO*/
.contact-forms [type="radio"]:not(:checked) + label:after,
.contact-forms [type="radio"]:checked + label:after {
    background: none repeat scroll 0 0 #4EA5D9;
}
[type="radio"]:not(:checked),
[type="radio"]:checked {
  position: absolute;
  left: -9999px;
}
[type="radio"]:not(:checked) + label,
[type="radio"]:checked + label {
    cursor: pointer;
    font-size: 14px;
    padding-left: 25px;
    position: relative;
    user-select: none;
    display: block;
}
.form-options [type="radio"]:not(:checked) + label,
.form-options [type="radio"]:checked + label {
    font-size: 12px;
    color: #A7AEB9;
}
.btn [type="radio"]:not(:checked) + label::before,
.btn [type="radio"] + label::before {
	top: 4px !important;
	height: 10px !important;
	width: 10px !important;
	left: 5px !important;
}
.btn [type="radio"]:not(:checked) + label::after,
.btn [type="radio"] + label::after {
	top: 7px !important;
    left: 8px !important;
}
[type="radio"]:not(:checked) + label:before, [type="radio"]:checked + label:before {
    background: none repeat scroll 0 0 #fff;
    border: 2px solid #c8c8c8;
    border-radius: 0;
    content: "";
    height: 16px;
    left: 0;
    position: absolute;
    top: -4px;
    width: 16px;
}
[type="radio"]:not(:checked) + label:after, [type="radio"]:checked + label:after {
    background: none repeat scroll 0 0 #C01908;
    content: "";
    font-size: 14px;
    height: 8px;
    width: 8px;
    left: 6px;
    margin-top: 0px;
    position: absolute;
    top: 2px;
    transition: all 0.2s ease 0s;
}
[type="radio"]:not(:checked) + label:after {
  opacity: 0;
  transform: scale(0);
}
[type="radio"]:checked + label:after {
  opacity: 1;
  transform: scale(1);
}
[type="radio"]:disabled:not(:checked) + label:before,
[type="radio"]:disabled:checked + label:before {
  box-shadow: none;
  border-color: #bbb;
  background-color: #ddd;
}
[type="radio"]:disabled:checked + label:after {
  color: #999;
}
[type="radio"]:disabled + label {
  color: #aaa;
}

/* radio RTL */
.rtl [type="radio"]:not(:checked) + label::before, .rtl [type="radio"]:checked + label::before {
	left: auto;
	right: 0;
	background: none repeat scroll 0 0 #fff;
	border: 1px solid #E5E5E5;
	border-radius: 50%;
	content: "";
	height: 30px;
	position: absolute;
	top: 50%;
	width: 30px;
    transform: translateY(-50%);
}
.rtl [type="radio"]:not(:checked) + label::after, .rtl [type="radio"]:checked + label::after {
    background: none repeat scroll 0 0 #871C1E;
    content: "";
    font-size: 14px;
    height: 10px;
    width: 10px;
    left: auto;
    margin-top: 0px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.2s ease 0s;
    right: 11px;
    border-radius: 50%;
}
.rtl [type="radio"]:not(:checked) + label, [type="radio"]:checked + label {
	padding-left: 0px;
}
.f-18 [type="radio"]:not(:checked) + label, [type="radio"]:checked + label {
	font-size: 18px;
}

.g-color {
  color: #e34133;
}

.g-bg-color {
  background-color: #e34133 !important;
}

.tw-color {
  color: #4EA5D9;
}

.fb-color {
  color: #4267B2;
}

.fb-bg-color {
  background-color: #4267B2;
}

.ln-color {
  color: #0077B5;
}

.ig-color {
  color: #2D333B;
}

.yt-color {
  color: #871b1e;
}

.transition1 {
  -webkit-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
}

.transition2 {
  -webkit-transition: all 0.4s cubic-bezier(0.13, 0.56, 0.38, 0.89) 0s;
  transition: all 0.4s cubic-bezier(0.13, 0.56, 0.38, 0.89) 0s;
}

/* scrollbar */
::-webkit-scrollbar-track {
  background-color: #e5e5e5;
}

::-webkit-scrollbar {
  width: 6px;
  background-color: #000;
}

::-webkit-scrollbar-thumb {
  background-color: #000;
}

* {
  scrollbar-color: #000 #e5e5e5;
  scrollbar-width: thin;
}

/* selection */
::-moz-selection {
  color: #fff;
  background: #000;
}

::selection {
  color: #fff;
  background: #000;
}

:focus {
  outline: none !important;
}

button:focus {
  outline: none !important;
}

p {
  word-break: break-word;
}

textarea {
  resize: vertical;
}

.fs-14 {
  font-size: 14px !important;
}

.line-height-small {
  line-height: 1.1;
}

.line-height-normal {
  line-height: 1.5;
}

.line-height-big {
  line-height: 1.8;
}

.hidden {
  display: none;
}

.text-center {
  text-align: center !important;
}

.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-underline {
  text-decoration: underline !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.light {
  font-weight: 300 !important;
}

.normal {
  font-weight: 400 !important;
}

.medium {
  font-weight: 500 !important;
}

.semibold {
  font-weight: 600 !important;
}

b, strong, .bold {
  font-weight: 700 !important;
}

.white {
  color: #fff !important;
}

.lightGray {
  color: #F9FAFC !important;
}

.gray {
  color: #e5e5e5 !important;
}

.midGray {
  color: #969696 !important;
}

.darkGray {
  color: #333 !important;
}

.red {
  color: #d5686c !important;
}

.black {
  color: #000 !important;
}

.green {
  color: #2C864F !important;
}

.white-bg {
  background-color: #fff !important;
}

.lightGray-bg {
  background-color: #F9FAFC !important;
}

.gray-bg {
  background-color: #e5e5e5 !important;
}

.midGray-bg {
  background-color: #969696 !important;
}

.darkGray-bg {
  background-color: #333 !important;
}

.red-bg {
  background-color: #871b1e !important;
}

.black-bg {
  background-color: #000 !important;
}

.green-bg {
  background-color: #2C864F !important;
}

.main-bg {
  background-color: #617B91 !important;
}

.second-bg {
  background-color: #d5686c !important;
}

.btn.white-bg {
  color: #333;
  border: 1px solid #e5e5e5;
}

.btn.gray-bg {
  color: #333;
}

.btn.lightGray-bg {
  color: #333;
}

.btn.gray-bg {
  color: #333;
}

.btn.midGray-bg {
  color: #969696;
}

.btn.darkGray-bg {
  color: #fff;
}

.btn.red-bg {
  color: #fff;
}

.btn.main-bg {
  color: #617B91;
  border: 1px solid #617B91;
}

.btn.second-bg {
  color: #d5686c;
  border: 1px solid #d5686c;
}

.btn.main-bg:hover {
  background-color: transparent !important;
  color: #617B91 !important;
}

.btn.second-bg:hover {
  background-color: #bf5559 !important;
  border-color: #bf5559 !important;
  color: #fff !important;
}

.btn.link-btn {
  padding: 0;
  font-size: 18px !important;
  color: #617B91;
  font-weight: 500;
}

.btn.link-btn:hover {
  opacity: .7;
}

.btn.box-shadow {
  -webkit-box-shadow: rgba(213, 104, 108, 0.2) 0px 20px 20px;
          box-shadow: rgba(213, 104, 108, 0.2) 0px 20px 20px;
}

.btn.box-shadow:hover {
  -webkit-box-shadow: rgba(213, 104, 108, 0.4) 0px 20px 20px;
          box-shadow: rgba(213, 104, 108, 0.4) 0px 20px 20px;
}

.btn-badge.white-bg:hover, body .btn.white-bg:hover, button.white-bg:hover {
  background-color: #000 !important;
  color: #fff !important;
  border-color: #000;
}

.btn-badge.lightGray-bg:hover, .btn.lightGray-bg:hover, button.lightGray-bg:hover {
  background-color: #e5e5e5 !important;
}

.btn-badge.gray-bg:hover, .btn.gray-bg:hover, button.gray-bg:hover {
  background-color: #969696 !important;
}

.btn-badge.midGray-bg:hover, .btn.midGray-bg:hover, button.midGray-bg:hover {
  background-color: #333 !important;
}

.btn-badge.darkGray-bg:hover, .btn.darkGray-bg:hover, button.darkGray-bg:hover {
  background-color: #666 !important;
}

.btn-badge.red-bg:hover, .btn.red-bg:hover, button.red-bg:hover {
  background-color: #680a00 !important;
}

.btn-badge.black-bg:hover, .btn.black-bg:hover, button.black-bg:hover {
  background-color: #969696 !important;
}

.btn.border-btn {
  border: 1px solid #E8EFF4;
  color: #617B91;
}

.btn.border-btn:hover {
  background: #bf5559;
  border: 1px solid rgba(0, 0, 0, 0);
  color: #fff;
}

.btn.underline-btn {
  position: relative;
}

.btn.underline-btn::before {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 10;
  height: 1px;
  width: 100%;
  content: "";
  background-color: #617B91;
}

.btn.fb-bg-color {
  background-color: #4267B2 !important;
}

.btn.fb-bg-color:hover {
  background-color: #224385 !important;
}

.btn.g-bg-color {
  background-color: #af2f23 !important;
}

.btn.g-bg-color:hover {
  background-color: #af2f23 !important;
}

.link.link-white:hover {
  color: #F9FAFC !important;
}

.link.link-lightGray:hover {
  color: #e5e5e5 !important;
}

.link.link-gray:hover {
  color: #969696 !important;
}

.link.link-midGray:hover {
  color: #333 !important;
}

.dark-form .link.link-midGray:hover {
  color: #fff !important;
}

.link.link-darkGray:hover {
  color: #000 !important;
}

.link.link-red:hover {
  color: #680a00 !important;
}

.link.link-black:hover {
  color: #666 !important;
}

.link:hover {
  text-decoration: underline;
}

.link-item {
  margin-right: 50px;
  position: relative;
}
.link-item > ul {
	position: absolute;
	top: 100%;
	left: 0;
	z-index: 1;
	background: #fff;
	border: 1px solid #e6eff5;
	padding: 10px 20px;
	min-width: 150px;
    pointer-events: none;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.25s ease-in-out 0s;
    visibility: hidden;
}
.link-item:hover > ul {
    pointer-events: auto;
    opacity: 1;
    transform: translateY(0px);
    visibility: visible;
}
.link-item:last-of-type {
  margin-right: 0;
}

.link-item .link:hover {
    color: #da262e;
}
.link-item .link {
  text-decoration: none;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  white-space: nowrap;
  line-height: 50px;
  font-size: 16px;
  color: #617B91;
  text-transform: uppercase;
}

.link-item .login-btn {
  padding: 13px 0 13px 50px;
  border-left: 1px solid rgba(97, 123, 145, 0.2);
  color: #d5686c !important;
}

.link-item .btn {
  font-size: 14px;
}

.invert {
  -webkit-filter: invert(1);
          filter: invert(1);
}

a:hover .invert {
  -webkit-filter: invert(0) !important;
          filter: invert(0) !important;
}

.m-auto {
  margin: auto;
}

.m-0 {
  margin: 0 !important;
}

.m-1 {
  margin: 10px !important;
}

.m-2 {
  margin: 20px !important;
}

.m-3 {
  margin: 30px !important;
}

.m-4 {
  margin: 40px !important;
}

.m-5 {
  margin: 50px !important;
}

.mt-auto {
  margin-top: auto !important;
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: 10px !important;
}

.mt-2 {
  margin-top: 20px;
}

.mt-3 {
  margin-top: 30px !important;
}

.mt-4 {
  margin-top: 40px !important;
}

.mt-5 {
  margin-top: 50px !important;
}

.mt-50 {
  margin-top: 50px !important;
}

.mt-80 {
  margin-top: 80px !important;
}

.mt-100 {
  margin-top: 100px !important;
}

.mt--100 {
  margin-top: -100px !important;
}

.mb-auto {
  margin-bottom: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: 10px !important;
}

.mb-2 {
  margin-bottom: 20px !important;
}

.mb-3 {
  margin-bottom: 30px !important;
}

.mb-4 {
  margin-bottom: 40px !important;
}

.mb-5 {
  margin-bottom: 50px !important;
}

.mb-6 {
  margin-bottom: 60px !important;
}

.mb-50 {
  margin-bottom: 50px !important;
}

.mb-80 {
  margin-bottom: 80px !important;
}

.mb-100 {
  margin-bottom: 100px !important;
}

.mb-150 {
  margin-bottom: 150px !important;
}

.ml-auto {
  margin-left: auto !important;
}

.ml-0 {
  margin-left: 0 !important;
}

.ml-05 {
  margin-left: 5px !important;
}

.ml-1 {
  margin-left: 10px !important;
}

.ml-2 {
  margin-left: 20px !important;
}

.ml-3 {
  margin-left: 30px !important;
}

.ml-4 {
  margin-left: 40px !important;
}

.ml-5 {
  margin-left: 50px !important;
}

.ml-6 {
  margin-left: 60px !important;
}

.mr-auto {
  margin-right: auto !important;
}

.mr-0 {
  margin-right: 0 !important;
}

.mr-05 {
  margin-right: 5px !important;
}

.mr-1 {
  margin-right: 10px !important;
}

.mr-2 {
  margin-right: 20px !important;
}

.mr-3 {
  margin-right: 30px !important;
}

.mr-4 {
  margin-right: 40px !important;
}

.mr-5 {
  margin-right: 50px !important;
}

.mr-6 {
  margin-right: 60px !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.mx-05 {
  margin-right: 5px !important;
  margin-left: 5px !important;
}

.mx-1 {
  margin-right: 10px !important;
  margin-left: 10px !important;
}

.mx-2 {
  margin-right: 20px !important;
  margin-left: 20px !important;
}

.mx-3 {
  margin-right: 30px !important;
  margin-left: 30px !important;
}

.mx-4 {
  margin-right: 40px !important;
  margin-left: 40px !important;
}

.mx-5 {
  margin-right: 50px !important;
  margin-left: 50px !important;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto !important;
}

.my-0 {
  margin-top: 0;
  margin-bottom: 0 !important;
}

.my-05 {
  margin-top: 5px !important;
  margin-bottom: 5px !important;
}

.my-1 {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}

.my-2 {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

.my-3 {
  margin-top: 30px !important;
  margin-bottom: 30px !important;
}

.my-4 {
  margin-top: 40px !important;
  margin-bottom: 40px !important;
}

.my-5 {
  margin-top: 50px !important;
  margin-bottom: 50px !important;
}

.my-80 {
  margin-top: 80px !important;
  margin-bottom: 80px !important;
}

.my-100 {
  margin-top: 100px !important;
  margin-bottom: 100px !important;
}

.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: 10px !important;
}

.p-2 {
  padding: 20px !important;
}

.p-3 {
  padding: 30px !important;
}

.p-4 {
  padding: 40px !important;
}

.p-5 {
  padding: 50px !important;
}

.p-8 {
  padding: 80px !important;
}

.p-10 {
  padding: 100px !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pt-1 {
  padding-top: 10px !important;
}

.pt-2 {
  padding-top: 20px !important;
}

.pt-25 {
  padding-top: 25px !important;
}

.pt-3 {
  padding-top: 30px !important;
}

.pt-4 {
  padding-top: 40px !important;
}

.pt-5 {
  padding-top: 50px !important;
}

.pt-6 {
  padding-top: 60px !important;
}

.pt-7 {
  padding-top: 70px !important;
}

.pt-100 {
  padding-top: 100px !important;
}

.pt-150 {
  padding-top: 150px !important;
}

.pt-200 {
  padding-top: 200px !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pb-1 {
  padding-bottom: 10px !important;
}

.pb-2 {
  padding-bottom: 20px !important;
}

.pb-25 {
  padding-bottom: 25px !important;
}

.pb-3 {
  padding-bottom: 30px !important;
}

.pb-4 {
  padding-bottom: 40px !important;
}

.pb-5 {
  padding-bottom: 50px !important;
}

.pb-7 {
  padding-bottom: 70px !important;
}

.pb-8 {
  padding-bottom: 80px !important;
}

.pb-100 {
  padding-bottom: 100px !important;
}

.pb-150 {
  padding-bottom: 150px !important;
}

.pb-200 {
  padding-bottom: 200px !important;
}

.pl-0 {
  padding-left: 0 !important;
}

.pl-1 {
  padding-left: 10px !important;
}

.pl-2 {
  padding-left: 20px !important;
}

.pl-3 {
  padding-left: 30px !important;
}

.pl-4 {
  padding-left: 40px !important;
}

.pl-5 {
  padding-left: 50px !important;
}

.pl-75 {
  padding-left: 75px !important;
}

.pl-100 {
  padding-left: 100px !important;
}

.pl-120 {
  padding-left: 120px !important;
}

.pl-150 {
  padding-left: 150px !important;
}

.pl-200 {
  padding-left: 200px !important;
}

.pr-0 {
  padding-right: 0 !important;
}

.pr-1 {
  padding-right: 10px !important;
}

.pr-2 {
  padding-right: 20px !important;
}

.pr-3 {
  padding-right: 30px !important;
}

.pr-4 {
  padding-right: 40px !important;
}

.pr-5 {
  padding-right: 50px !important;
}

.pr-75 {
  padding-right: 75px !important;
}

.pr-100 {
  padding-right: 100px !important;
}

.pr-120 {
  padding-right: 120px !important;
}

.pr-150 {
  padding-right: 150px !important;
}

.pr-200 {
  padding-right: 200px !important;
}

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.py-1 {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.py-2 {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.py-3 {
  padding-top: 30px !important;
  padding-bottom: 30px !important;
}

.py-4 {
  padding-top: 40px !important;
  padding-bottom: 40px !important;
}

.py-5 {
  padding-top: 50px !important;
  padding-bottom: 50px !important;
}

.py-8 {
  padding-top: 80px !important;
  padding-bottom: 80px !important;
}

.py-100 {
  padding-top: 100px !important;
  padding-bottom: 100px !important;
}

.py-200 {
  padding-top: 200px !important;
  padding-bottom: 200px !important;
}

.px-0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.px-1 {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.px-2 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}

.px-3 {
  padding-left: 30px !important;
  padding-right: 30px !important;
}

.px-4 {
  padding-left: 40px !important;
  padding-right: 40px !important;
}

.px-5 {
  padding-left: 50px !important;
  padding-right: 50px !important;
}

.px-100 {
  padding-left: 100px !important;
  padding-right: 100px !important;
}

.px-120 {
  padding-left: 120px !important;
  padding-right: 120px !important;
}

.px-200 {
  padding-left: 200px !important;
  padding-right: 200px !important;
}

.f-0 {
  font-size: 0 !important;
}

.f-10 {
  font-size: 10px !important;
}

.f-12 {
  font-size: 12px !important;
}

.f-14 {
  font-size: 14px !important;
}

.f-16 {
  font-size: 16px !important;
}

.f-18 {
  font-size: 18px !important;
}

.f-22 {
  font-size: 22px !important;
}

.f-24 {
  font-size: 24px !important;
}

.f-36 {
  font-size: 36px !important;
}

.f-48 {
  font-size: 48px !important;
}

.f-64 {
  font-size: 64px !important;
}

.no-border {
  border: none !important;
}

.left-border {
  border-left: 1px solid #e5e5e5 !important;
}

.top-border {
  border-top: 1px solid #e5e5e5 !important;
}

.right-border {
  border-right: 1px solid #e5e5e5 !important;
}

.bottom-border {
  border-bottom: 1px solid #e5e5e5 !important;
}

h1, .h1 {
  font-weight: 700;
  font-size: 64px !important;
}

h2, .h2-big {
  font-weight: normal;
  font-size: 48px !important;
}

h2, .h2 {
  font-weight: normal;
  font-size: 36px !important;
}

h3, .h3 {
  font-weight: normal;
  font-size: 24px !important;
}

h4, .h4 {
  font-weight: normal;
  font-size: 20px !important;
}

h5, .h5 {
  font-weight: normal;
  font-size: 16px !important;
}

h6, .h6 {
  font-weight: normal;
  font-size: 14px !important;
}

h1.big-title {
  font-size: 64px !important;
}

h1.secondary, .h1.secondary {
  color: #333;
}

h2.secondary, .h2.secondary {
  color: #333;
}

h3.secondary, .h3.secondary {
  color: #333;
}

h4.secondary, .h4.secondary {
  color: #333;
}

h5.secondary, .h5.secondary {
  color: #333;
}

.with-line {
  position: relative;
}

.with-line::before {
  content: "";
  width: 40px;
  height: 2px;
  background: #871b1e;
  position: absolute;
  bottom: -24px;
  left: 0;
}

a, button {
  -webkit-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
}

img {
  max-width: 100%;
  display: block;
}

.d-block {
  display: block;
}

.d-inline-block {
  display: inline-block;
}

hr {
  border: none;
  height: 1px;
  width: 100%;
  background: #e5e5e5;
  margin: 30px 0;
}

.hide, .d-none {
  display: none !important;
}

.row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.row-vertical {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.container {
  margin: auto;
  max-width: 1420px;
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}

.container640 {
  margin: auto;
  max-width: 640px;
  width: 640px;
  padding-left: 15px;
  padding-right: 15px;
}

.container-fluid {
  margin: auto;
  max-width: 100%;
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}

.transition1 {
  -webkit-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
}

.transition2 {
  -webkit-transition: all 0.4s cubic-bezier(0.13, 0.56, 0.38, 0.89) 0s;
  transition: all 0.4s cubic-bezier(0.13, 0.56, 0.38, 0.89) 0s;
}

.overflow {
  overflow: hidden;
}

.float-right {
  float: right !important;
}

.float-left {
  float: left !important;
}

.flex-column {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
      -ms-flex-direction: column !important;
          flex-direction: column !important;
}

.flex-row {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: normal !important;
      -ms-flex-direction: row !important;
          flex-direction: row !important;
}

.flex {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
}

.nowrap {
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
}

.h100 {
  height: 100%;
}

.h100vh {
  height: 100vh;
}

.h-auto {
  height: auto !important;
}

.h450 {
  height: 23.45vw;
}

.h360 {
  height: 18.75vw;
}

.d-block {
  display: block;
}

.w100 {
  width: 100%;
}

.aic {
  -webkit-box-align: center !important;
      -ms-flex-align: center !important;
          align-items: center !important;
}

.ail {
  -webkit-box-align: start !important;
      -ms-flex-align: start !important;
          align-items: flex-start !important;
}

.air {
  -webkit-box-align: end !important;
      -ms-flex-align: end !important;
          align-items: flex-end !important;
}

.jcr {
  -webkit-box-pack: end !important;
      -ms-flex-pack: end !important;
          justify-content: flex-end !important;
}

.jcl {
  -webkit-box-pack: start !important;
      -ms-flex-pack: start !important;
          justify-content: flex-start !important;
}

.jcsb {
  -webkit-box-pack: justify !important;
      -ms-flex-pack: justify !important;
          justify-content: space-between !important;
}

.jcc {
  -webkit-box-pack: center !important;
      -ms-flex-pack: center !important;
          justify-content: center !important;
}

[class*='col-'] {
  position: relative;
}

.big-gap {
  margin-right: -25px;
  margin-left: -25px;
}

.big-gap [class*='col-'] {
  padding-left: 25px;
  padding-right: 25px;
}

.normal-gap [class*='col-'] {
  padding-left: 15px;
  padding-right: 15px;
}

.small-gap {
  margin-right: -8px;
  margin-left: -8px;
}

.small-gap [class*='col-'] {
  padding-left: 8px;
  padding-right: 8px;
}

.col-12 {
  padding-left: 15px;
  padding-right: 15px;
  width: 100%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  max-width: 100%;
}

.col-10 {
  padding-left: 15px;
  padding-right: 15px;
  width: 83.333333%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 83.333333%;
          flex: 0 0 83.333333%;
  max-width: 83.333333%;
}

.col-9 {
  padding-left: 15px;
  padding-right: 15px;
  width: 75%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 75%;
          flex: 0 0 75%;
  max-width: 75%;
}

.col-8 {
  padding-left: 15px;
  padding-right: 15px;
  width: 66.6666%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 66.6666%;
          flex: 0 0 66.6666%;
  max-width: 66.6666%;
}

.col-7 {
  padding-left: 15px;
  padding-right: 15px;
  width: 58.333333%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 58.333333%;
          flex: 0 0 58.333333%;
  max-width: 58.333333%;
}

.col-6 {
  padding-left: 15px;
  padding-right: 15px;
  width: 50%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
  max-width: 50%;
}

.col-5 {
  padding-left: 15px;
  padding-right: 15px;
  width: 41.666667%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 41.666667%;
          flex: 0 0 41.666667%;
  max-width: 41.666667%;
}

.col-4 {
  padding-left: 15px;
  padding-right: 15px;
  width: 33.3333%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 33.3333%;
          flex: 0 0 33.3333%;
  max-width: 33.3333%;
}

.col-3 {
  padding-left: 15px;
  padding-right: 15px;
  width: 25%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  max-width: 25%;
}

.col-2-5 {
  padding-left: 15px;
  padding-right: 15px;
  width: 25%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  max-width: 25%;
}

.col-2 {
  padding-left: 15px;
  padding-right: 15px;
  width: 16.666667%;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 16.666667%;
          flex: 0 0 16.666667%;
  max-width: 16.666667%;
}

.col {
  padding-left: 15px;
  padding-right: 15px;
  width: auto;
}

.offset-1 {
  margin-left: 8.333333%;
}

.offset-2 {
  margin-left: 16.666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.333333%;
}

.offset-5 {
  margin-left: 41.666667%;
}

.offset-6 {
  margin-left: 50%;
}

@media (max-width: 767px) {
  ol {
    padding-left: 20px;
  }
  .col-12, .col-11, .col-10, .col-9, .col-8, .col-7, .col-6, .col-5, .col-4, .col-3, .col-2-5, .col-2 {
    position: relative;
    width: 100%;
    padding-right: 10px;
    padding-left: 10px;
    max-width: 100%;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
  }
  .container,
  .container-fluid {
    padding-left: 10px;
    padding-right: 10px;
  }
  .container640 {
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
  }
  .row-vertical {
    margin-right: -10px;
    margin-left: -10px;
  }
}

.txt-holder {
  color: #617B91;
}

.txt-holder h5 {
  font-size: 18px !important;
  color: #d5686c;
  margin-bottom: 20px;
  font-weight: 700;
  text-transform: uppercase;
}

.txt-holder h4 {
  font-size: 18px !important;
  color: #d5686c;
  margin-bottom: 20px;
  font-weight: 700;
  text-transform: uppercase;
}

.txt-holder h3 {
  font-size: 36px !important;
  margin-bottom: 40px;
  font-weight: 700;
}

.txt-holder p {
  font-size: 18px;
  color: #95A9BA;
  margin-bottom: 40px;
  line-height: 1.6;
}

.img-holder .icon-holder {
  position: absolute;
  top: 50px;
  left: 15px;
  z-index: 1;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.level-item .icon-holder img {
	width: 34px;
	height: 30px;
}
.fancybox__content svg, .fancybox__content svg path {
	stroke: #d5686c !important;
	stroke-width: 2px !important;
	width: 50px !important;
	height: 50px !important;
}

.img-holder .icon-holder img {
  height: 118px;
  border-radius: 20px;
}

.img-holder img {
  border-radius: 20px;
}

body {
  font-size: 16px;
  font-weight: 400;
  color: #617B91;
  line-height: 1.4;
}

body.show-popup {
  overflow: hidden;
}

body main {
  position: relative;
  padding-top: 100px;
}

section {
  position: relative;
}

header {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0;
  height: 100px;
  width: 100%;
  -webkit-transition: background-color 0.25s ease-in-out 0s, border 0.25s ease-in-out 0s;
  transition: background-color 0.25s ease-in-out 0s, border 0.25s ease-in-out 0s;
  background: white;
  border-bottom: 1px solid #E6EFF5;
}
.max640 {
	max-width: 660px;
	margin: auto;
}
.right-menu {
  margin-left: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
}

.dropdown-button {
  z-index: 11;
  background: #fff;
  cursor: pointer;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.dropdown {
  position: relative;
}

.dropdown > .dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 150px;
  width: auto;
  z-index: 12;
  background: #fff;
  -webkit-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  -webkit-transform: translateY(-10px);
          transform: translateY(-10px);
  padding: 25px;
  border-radius: 5px;
  border: 1px solid #e5e5e5;
  margin-top: 10px;
}

.dropdown > .dropdown-menu.drop-right {
  right: 0;
  left: auto;
}

.dropdown > .dropdown-menu > li {
  padding: 0;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 20px;
}

.dropdown > .dropdown-menu > li:last-of-type {
  margin-bottom: 0px;
}

.dropdown > .dropdown-menu > li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 14px;
  color: #fff;
  -webkit-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
  white-space: nowrap;
}

.dropdown > .dropdown-menu > li a img {
  margin-right: 10px;
  width: 25px;
  height: 25px;
}

.dropdown > .dropdown-menu > li a:hover {
  color: #871b1e;
}

.dropdown.opened > .dropdown-menu {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  -webkit-transform: translateY(0px);
          transform: translateY(0px);
}

.dropdown.opened > .dropdown-button {
  color: #333;
}

.dropdown.opened > .dropdown-button:hover {
  color: #333;
  background: #fff;
}

.dropdown.opened > .panel-button {
  z-index: 13;
}

.btn {
  padding: 13px 24px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
  border-radius: 15px;
  font-weight: 700;
  font-size: 16px;
  text-align: center;
  cursor: pointer;
  white-space: nowrap;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  color: #617B91;
}

.btn:hover {
  color: #d5686c;
  background: #d5686c;
}

.btn.btn-sm {
  min-height: 30px;
  padding: 0 15px;
}

.btn.btn-xs {
  min-height: 24px;
  text-transform: uppercase;
  padding: 0 15px;
  font-size: 10px !important;
}

.btn.btn-xs.no-hover {
  pointer-events: none;
}

.btn.btn-wide {
  padding: 0 50px;
}

.btn.btn-tall {
  height: 80px;
  font-size: 20px;
}

.btn.btn-border {
  border-radius: 50px;
  border: 1px solid #e5e5e5;
}

.btn.btn-border:hover {
  border-color: #000;
  background: #000;
}

.btn.btn-border:hover img {
  -webkit-filter: invert(1);
          filter: invert(1);
}

.btn.btn-badge {
  width: 40px;
  padding: 0 10px;
}

.overlay {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100vh;
  left: 0;
  -webkit-transition: all 0.5s ease-in-out 0s;
  transition: all 0.5s ease-in-out 0s;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  background: rgba(0, 0, 0, 0.5);
}

.overlay .popup {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transition: all 0.4s cubic-bezier(0.13, 0.56, 0.38, 0.89) 0s;
  transition: all 0.4s cubic-bezier(0.13, 0.56, 0.38, 0.89) 0s;
  -webkit-transform: translate(-50%, -50%) scale(1.05);
          transform: translate(-50%, -50%) scale(1.05);
  width: 100%;
  max-width: 600px;
  z-index: 1002;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  background: #fff;
  padding: 70px 80px;
}

.overlay .popup.small-padding {
  padding: 25px 50px;
}

.overlay .popup.small-padding .popup-body {
  padding: 0px 50px 25px 50px;
}

.overlay .popup .popup-body {
  padding: 0 80px 60px;
}

.overlay .popup .popup-header {
  padding: 30px 0;
  border-bottom: 1px solid #E5E5E5;
}

.show-popup .overlay {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.show-popup .overlay .popup.show {
  -webkit-transform: translate(-50%, -50%) scale(1);
          transform: translate(-50%, -50%) scale(1);
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.close_page {
  position: absolute;
  top: 0;
  right: 0;
  color: #e5e5e5;
  font-size: 40px;
  padding: 20px;
  cursor: pointer;
}

.close_page:hover {
  color: #333;
}

.close-popup {
  cursor: pointer;
}

.close-popup:hover {
  opacity: 0.5;
}

.input-container {
  position: relative;
  width: 100%;
  margin-bottom: 20px;
}

.line-input {
  width: 100%;
  height: 60px;
  border: none;
  border-bottom: 1px solid #e5e5e5;
  font-size: 16px;
  background: #fff;
}

.line-input.no-bg {
  background: rgba(0, 0, 0, 0);
}

.line-input:focus, .line-input:active {
  border-color: #000;
}

.round-select {
  width: auto;
  height: 56px;
  border-radius: 30px;
  padding: 0px 45px 0px 25px;
  border: 1px solid #e5e5e5;
  font-size: 18px;
  /* background: #F9FAFC url(../images/arrow-down.svg) no-repeat right 30px center; */
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
}

.round-select:hover {
  border-color: #bcbcbc;
  cursor: pointer;
}

.line-select {
  width: 100%;
  height: 60px;
  border: none;
  border-bottom: 1px solid #D4D8DD;
  font-size: 18px;
  /* background: #fff url(../images/select-arrow.png) no-repeat right 15px center; */
  -moz-appearance: none;
  -webkit-appearance: none;
}

select:valid + .select-label {
  opacity: 1;
}

select:invalid {
  color: gray;
  outline: 0 !important;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

select:valid + .select-label {
  opacity: 0.3;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

select + .select-label {
  position: absolute;
  -webkit-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
  top: 0;
  left: 0;
  -webkit-transform: translateY(-50%);
  transform: translateY(0%);
  opacity: 0;
  font-size: 14px;
}

.clear_select {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 40px;
  font-size: 30px;
  display: none;
  line-height: 1;
  height: 40px;
  cursor: pointer;
}

.clear_select:hover {
  opacity: 0.5;
}

textarea.line-input {
  min-height: 250px;
  padding: 25px;
  border: 1px solid #e5e5e5;
}

.line-input:placeholder-shown + .input-label {
  opacity: 0;
  top: 17px;
  font-size: 18px;
}

.input-label {
  display: block;
  width: 100%;
  position: absolute;
  bottom: 18px;
  left: 1px;
  opacity: 0.4;
  top: -10px;
  font-size: 14px;
  -webkit-transition: all 0.4s cubic-bezier(0.13, 0.56, 0.38, 0.89) 0s;
  transition: all 0.4s cubic-bezier(0.13, 0.56, 0.38, 0.89) 0s;
  pointer-events: none;
  text-align: left;
}

.big-header-image {
  height: calc(100vh - 100px);
  padding: 0 !important;
  margin: 0;
  position: relative;
}

.small-header-image {
  min-height: 600px;
  padding: 0 !important;
  margin: 0;
  position: relative;
}

.small-header-image .image-overlay img {
  min-height: 600px;
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  max-height: 600px;
}

.smaller-header-image {
  min-height: 300px;
  padding: 0 !important;
  margin: 0;
  position: relative;
}

.panel {
  background: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding: 40px 30px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.panel.big-padding {
  padding: 50px 60px;
}

.panel.dark-panel {
  background: #333;
}

.panel.with-shadow {
  -webkit-box-shadow: 20px 0px 30px rgba(212, 216, 221, 0.15);
          box-shadow: 20px 0px 30px rgba(212, 216, 221, 0.15);
}

.panel .panel-header {
  width: 100%;
  padding: 30px;
  text-align: center;
}

.panel .panel-header.help-panel-header {
  padding: 50px 30px 30px;
  height: 180px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.panel .panel-header.help-panel-header p {
  font-size: 16px;
}

.panel .panel-body {
  width: 100%;
  padding: 50px;
}

.hero-container h1 {
  font-size: 64px !important;
  color: #fff;
  font-weight: 500;
  margin-bottom: 20px;
}

.hero-container h3 {
  margin-bottom: 70px;
  color: #fff;
}

.hero-banner {
  position: relative;
}

.hero-banner img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

footer {
  background: #7693ac url("../images/footer-bg.svg") no-repeat top 130px center;
  color: #fff;
}

footer ul li {
  font-weight: 300;
  font-size: 14px;
}

footer ul:last-of-type {
  margin-bottom: 0 !important;
}

footer .footer-menu-title {
  font-weight: 600;
  margin-bottom: 30px;
}

footer .footer-cta-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 65px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

footer .footer-cta-content .footer-cta-txt h3 {
  font-size: 36px !important;
  margin-bottom: 10px;
  font-weight: 700;
}

footer .footer-cta-content .footer-cta-txt p {
  font-size: 16px;
}

footer .footer-cta-content .footer-cta-btn {
  position: relative;
}

footer .footer-cta-content .footer-cta-btn .btn {
  position: absolute;
  top: 50%;
  left: -50px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: 10;
}

footer .footer-links-content {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 120px 0;
}

footer .footer-links-content .logo-holder {
  margin-bottom: 40px;
}

footer .footer-links-content .social-icons-holder {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

footer .footer-links-content .social-icons-holder li {
  margin-right: 15px;
}

footer .footer-links-content .footer-links-item h5 {
  font-weight: 700;
}

footer .footer-links-content .footer-links-item ul li a {
  color: #fff;
  font-size: 16px;
  margin-bottom: 15px;
  display: block;
}

footer .footer-links-content .footer-links-item ul li a:hover {
  opacity: .7;
}

footer .footer-cta-content {
  padding: 30px 0;
}

footer .footer-cta-content .presale-cta h3 {
  font-size: 36px;
  font-weight: 100;
}

footer .footer-cta-content .presale-cta h3 a {
  font-weight: 700;
  color: #fff;
  border-bottom: 2px solid #fff;
}

footer .footer-cta-content .presale-cta h3 a:hover {
  opacity: .7;
}

footer .footer-cta-content .all-right-txt {
  text-align: right;
  font-size: 16px;
}

.footer-logo {
  width: 140px;
}

.footer-social {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.footer-social a {
  display: block;
  height: 45px;
  width: 45px;
  margin: 0 8px;
}

.footer-social a:hover {
  opacity: 0.4;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;
  top: calc(100px + 20px);
}

.order-md-first {
  -webkit-box-ordinal-group: 0 !important;
      -ms-flex-order: -1 !important;
          order: -1 !important;
}

.order-md-0 {
  -webkit-box-ordinal-group: 1 !important;
      -ms-flex-order: 0 !important;
          order: 0 !important;
}

.order-md-1 {
  -webkit-box-ordinal-group: 2 !important;
      -ms-flex-order: 1 !important;
          order: 1 !important;
}

.order-md-2 {
  -webkit-box-ordinal-group: 3 !important;
      -ms-flex-order: 2 !important;
          order: 2 !important;
}

.order-md-3 {
  -webkit-box-ordinal-group: 4 !important;
      -ms-flex-order: 3 !important;
          order: 3 !important;
}

.order-md-4 {
  -webkit-box-ordinal-group: 5 !important;
      -ms-flex-order: 4 !important;
          order: 4 !important;
}

.order-md-5 {
  -webkit-box-ordinal-group: 6 !important;
      -ms-flex-order: 5 !important;
          order: 5 !important;
}

.order-md-last {
  -webkit-box-ordinal-group: 7 !important;
      -ms-flex-order: 6 !important;
          order: 6 !important;
}

body .mobile {
  display: none !important;
}

body .mobile-flex {
  display: none !important;
}

body .mobile-inline {
  display: none !important;
}

body .desktop {
  display: block !important;
}

body .desktop-flex {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
}

body .desktop-inline {
  display: inline-block !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 40px white inset !important;
  box-shadow: 0 0 0 40px white inset !important;
  -webkit-filter: none !important;
          filter: none !important;
}

.accordion-toggle {
  cursor: pointer;
  margin: 0;
  position: relative;
  padding: 24px 0 24px 24px;
  border-bottom: 1px solid #E6EFF5;
}

.accordion-toggle:first-of-type {
  border-top: 1px solid #E6EFF5;
}

.accordion-content {
  display: none;
}

.accordion-toggle::before {
  position: absolute;
  top: 30px;
  left: 0;
  z-index: 10;
  content: "";
  background: url(../images/accodion-icon.svg) no-repeat center center/contain;
  width: 15px;
  height: 15px;
}

.accordion-content {
  padding: 50px 0;
  border-bottom: 1px solid #E6EFF5;
}

.accordion-content p {
  margin-bottom: 50px;
  font-size: 18px;
}

.accordion-content p:last-of-type {
  margin-bottom: 0;
}

.accordion-toggle.active {
  color: #d5686c;
}

.accordion-toggle.active::before {
  background: url(../images/active-accodion-icon.svg) no-repeat center center/contain;
}

.page-404-item {
  padding: 30vh 0;
  text-align: center;
}

.page-404-item h1 {
  font-size: 64px;
  color: #d5686c;
  margin-bottom: 20px;
}

.page-404-item p {
  font-size: 24px;
  color: #617B91;
  margin-bottom: 100px;
}

.thank-you-container {
  padding: 0;
}

.thank-you-container .txt-holder {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
}

.thank-you-container .txt-holder h1 {
  font-size: 64px !important;
  font-weight: 700;
  margin-bottom: 30px;
  white-space: nowrap;
}

.thank-you-container .txt-holder p {
  font-size: 24px !important;
  font-weight: 300;
  margin-bottom: 60px;
  color: #fff;
}

.accessibility-content .heading-content,
.privacy-content .heading-content {
  padding: 120px 15px;
}

.accessibility-content .heading-content h1,
.privacy-content .heading-content h1 {
  font-size: 64px;
  margin-bottom: 30px;
  color: #d5686c;
  font-weight: 600;
}

.accessibility-content .heading-content p,
.privacy-content .heading-content p {
  font-size: 24px;
  color: #617B91;
  font-weight: 600;
}

.accessibility-content .accessibility-item p,
.accessibility-content .privacy-item p,
.privacy-content .accessibility-item p,
.privacy-content .privacy-item p {
  line-height: 1.6;
  font-size: 24px;
  color: #617B91;
  margin-bottom: 25px;
}

.accessibility-content .accessibility-item ul,
.accessibility-content .privacy-item ul,
.privacy-content .accessibility-item ul,
.privacy-content .privacy-item ul {
  margin-bottom: 25px;
  list-style: disc;
  padding-left: 25px;
}

.accessibility-content .accessibility-item ul li,
.accessibility-content .privacy-item ul li,
.privacy-content .accessibility-item ul li,
.privacy-content .privacy-item ul li {
  line-height: 1.6;
  font-size: 24px;
  color: #617B91;
}

.accessibility-content .accessibility-item a,
.accessibility-content .privacy-item a,
.privacy-content .accessibility-item a,
.privacy-content .privacy-item a {
  color: #d5686c;
  border-bottom: 2px solid #d5686c;
}

.accessibility-content .accessibility-item a:hover,
.accessibility-content .privacy-item a:hover,
.privacy-content .accessibility-item a:hover,
.privacy-content .privacy-item a:hover {
  opacity: .7;
}

.accessibility-content .accessibility-item h3,
.accessibility-content .privacy-item h3,
.privacy-content .accessibility-item h3,
.privacy-content .privacy-item h3 {
  font-size: 36px !important;
  color: #617B91;
  font-weight: 600;
  margin-bottom: 25px;
}

.main-img-section .main-img-container {
  padding: 0;
}

.main-img-section .main-img-container .main-img-content {
  margin: 0;
}

.main-img-section .main-img-container .main-img-content .main-img-item {
  padding: 0;
  position: relative;
}

.main-img-section .main-img-container .main-img-content .main-img-item .heading-holder {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    color: #fff;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
}

input, select, .select2, textarea {
  background: #fff;
  border: 0;
  padding: 15px;
  border-radius: 15px;
  width: 100%;
  color: #95A9BA;
  -webkit-box-shadow: #EDF1F5 0px 50px 50px;
          box-shadow: #EDF1F5 0px 50px 50px;
  font-size: 18px;
}

.select2-container--default .select2-selection--single {
  border: 0 !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #95A9BA !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 50% !important;
  right: 15px !important;
  -webkit-transform: translateY(-50%) !important;
          transform: translateY(-50%) !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  border-color: #95A9BA transparent transparent transparent !important;
}

.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
  background-color: #7693ac !important;
  color: #fff !important;
}

.select2-results__option--selectable {
  color: #7693ac !important;
}

.select2-container--open .select2-dropdown--below {
  border: 1px solid #7693ac !important;
}

.select2-container--default .select2-results__option--selected {
  background-color: #e6eaee !important;
  color: #fff !important;
}

::-webkit-input-placeholder {
  opacity: 1;
}

:-ms-input-placeholder {
  opacity: 1;
}

::-ms-input-placeholder {
  opacity: 1;
}

::placeholder {
  opacity: 1;
}

.txt-editor-content {
  color: #617B91;
  font-size: 24px;
}

.txt-editor-content ul,
.txt-editor-content p {
  margin-bottom: 30px;
}

.txt-editor-content img {
  margin: 100px 0;
}

.txt-editor-content h3 {
  font-size: 36px !important;
  margin-bottom: 50px;
  font-weight: 700;
}

.contact-us-section {
  padding: 150px 15px;
  background: #f9fafc;
}

.contact-us-section .contact-us-form .contact-us-item .txt-holder {
  padding-right: 80px;
}

.contact-us-section .contact-us-form .contact-us-item .txt-holder h5 {
  color: #d5686c;
  margin-bottom: 40px;
  font-weight: 700;
}

.contact-us-section .contact-us-form .contact-us-item .txt-holder h2 {
  font-size: 48px !important;
  color: #617B91;
  margin-bottom: 40px;
  font-weight: 700;
}

.contact-us-section .contact-us-form .contact-us-item .txt-holder p {
  font-size: 24px !important;
  color: #617B91;
  margin-bottom: 40px;
  font-weight: 100;
}

.mw-900 {
  max-width: 900px;
  width: 100%;
  margin: auto;
}

.mw-1000 {
  max-width: 1000px;
  width: 100%;
  margin: auto;
}

.mw-1240 {
  max-width: 1240px;
  width: 100%;
  margin: auto;
}

.w-100 {
  width: 100%;
}

.learn-page .blog-group-container .blog-group-content .blog-group-item .txt-holder p:last-of-type {
  margin-bottom: 0;
}

.learn-page .blog-group-container .blog-group-content .blog-group-item .txt-holder h3 {
  font-size: 24px !important;
}

.blog-group-container {
  padding-left: 15px;
  padding-right: 15px;
}

.blog-group-container .blog-group-content {
  margin-right: -25px;
  margin-left: -25px;
}

.blog-group-container .blog-group-content .col-4 {
  padding-left: 25px;
  padding-right: 25px;
}

.blog-group-container .blog-group-content .blog-group-item {
  -webkit-box-shadow: 0 50px 50px #EDF1F5;
          box-shadow: 0 50px 50px #EDF1F5;
  margin-bottom: 50px;
  border-radius: 0 0 25px 25px;
}

.blog-group-container .blog-group-content .blog-group-item .img-holder {
  position: relative;
}

.blog-group-container .blog-group-content .blog-group-item .img-holder:hover .play-holder span::before {
  width: 150px;
  height: 150px;
}

.blog-group-container .blog-group-content .blog-group-item .img-holder .play-holder {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 11;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  cursor: pointer;
}

.blog-group-container .blog-group-content .blog-group-item .img-holder .play-holder span {
  position: relative;
  display: block;
  width: 70px;
}

.blog-group-container .blog-group-content .blog-group-item .img-holder .play-holder span::before {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -1;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  content: "";
  background: #d5686c;
  width: 0;
  height: 0;
  border-radius: 50%;
  -webkit-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
  opacity: .1;
}

.blog-group-container .blog-group-content .blog-group-item .img-holder img {
  border-radius: 20px 20px 0 0;
}

.blog-group-container .blog-group-content .blog-group-item .txt-holder {
  background-color: #fff;
  padding: 50px;
  border-radius: 0 0 25px 25px;
  min-height: 220px;
}

.blog-group-container .blog-group-content .blog-group-item .txt-holder h3 {
  color: #617B91;
  margin-bottom: 15px;
  font-size: 24px !important;
  font-weight: 700;
}

.blog-group-container .blog-group-content .blog-group-item .txt-holder h5 {
  color: #d5686c;
  margin-bottom: 30px;
  font-size: 18px !important;
  font-weight: 700;
}

.blog-group-container .blog-group-content .blog-group-item .txt-holder p {
  color: #617B91;
  margin-bottom: 50px;
  font-size: 18px !important;
}

.blog-group-container .blog-group-content .blog-group-item .txt-holder .btn {
  font-weight: 700;
}

.filter-container .filter-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid #E6EFF5;
}

.filter-container .filter-content .filter-heading h2 {
  font-size: 48px !important;
  font-weight: 700;
  color: #617B91;
}

.filter-container .filter-content .filter-items {
  padding: 100px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: flex-end;
      -ms-flex-pack: flex-end;
          justify-content: flex-end;
  flex-wrap: nowrap;
}

.filter-container .filter-content .filter-items .filter-item {
  margin-right: 20px;
}

.filter-container .filter-content .filter-items .filter-item:last-of-type {
  margin-right: 0;
}

.filter-container .filter-content .filter-items .filter-item .btn.search-btn {
    padding: 9px 4px !important;
    height: 50px;
    width: 60px;
}
#search-form {
    position: relative;
    height: 50px;
}
.search-submit-button {
	position: absolute;
	top: 0;
	height: 46px;
	right: 0;
	width: 50px;
	background: rgba(0,0,0,0);
	font-size: 18px;
	z-index: 1;
}
#search-form form{
    position: absolute;
    right: 0;
    height: 50px;
    top: 0;
    max-width: 300px;
    min-width: 300px;
    width: 100%;
    box-shadow: 0 0 20px 0 rgba(0,0,0,0.1);
    border-radius: 20px;
}
.blog-full-container .heading-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.blog-full-container .heading-content h1 {
  font-size: 36px !important;
  color: #d5686c;
  padding-right: 20px;
}

.blog-full-container .heading-content .author-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.blog-full-container .heading-content .author-container .txt-holder {
  margin-left: 20px;
  line-height: 1.2;
  font-size: 18px;
  color: #617B91;
}

.blog-full-container .heading-content .author-container .txt-holder strong {
  font-weight: 700;
  white-space: nowrap;
}
.author-container .img-holder {
	width: 60px;
}
.blog-full-container .heading-content .author-container .txt-holder p {
  font-weight: 400;
  color: #95A9BA;
  margin-bottom: 0;
}
.social-icons-content {
	position: fixed;
	top: 200px;
	right: 55px;
	z-index: 1;
	display: flex;
	flex-direction: column;
}
.social-icons-item {
	width: 45px;
	height: 45px;
	margin-bottom: 5px;
}
.social-icons-item span:hover,
.social-icons-item a:hover {
    cursor: pointer;
    opacity: 0.6;
}
.social-icons-item span,
.social-icons-item a {
    transition: all 0.25s ease-in-out 0s;
	display: flex;
	width: 45px;
	height: 45px;
	background: #d5686c;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
}
.social-icons-item img {
	width: 17px;
	height: 20px;
}
.blog-full-container .blog-full-item {
  padding-bottom: 100px;
  border-bottom: 1px solid #E6EFF5;
}

.blog-full-offer-section .blog-full-offer-heading {
  padding: 100px 0;
}

.blog-full-offer-section .blog-full-offer-cta {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #95A9BA;
}

.blog-full-offer-section .blog-full-offer-cta .txt-holder {
  color: #fff;
  padding: 40px 40px;
}

.blog-full-offer-section .blog-full-offer-cta .txt-holder h3 {
  margin-bottom: 20px;
}

.blog-full-offer-section .blog-full-offer-cta .txt-holder p {
  margin-bottom: 40px;
  color: #fff;
}

.blog-full-offer-section .blog-full-offer-cta .img-holder {
  padding: 30px 0 0 0;
}

.blog-full-offer-section .blog-full-offer-cta .img-holder img {
  margin-left: auto;
}

.help-center-main-section {
  padding: 150px 0;
}

.help-center-cta-section {
  background: #F9FAFC;
  padding: 150px 0;
}

.help-center-cta-section .txt-holder p {
  margin-bottom: 40px;
}

.help-center-cta-section .img-holder img {
  border-radius: 20px;
}

.main-careers-section {
  padding: 150px 0;
  background-color: #F9FAFC;
}

.main-careers-section .txt-holder {
  padding-right: 100px;
}

.img-section {
  padding: 150px 0;
}

.open-positions-section {
  padding: 200px 0;
}

.open-positions-section .open-positions-item {
  -webkit-box-shadow: 0 50px 50px #EDF1F5;
          box-shadow: 0 50px 50px #EDF1F5;
  margin-bottom: 30px;
  border-radius: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 50px;
  background: #fff;
}

.open-positions-section .open-positions-item:last-of-type {
  margin-bottom: 0;
}

.open-positions-section .open-positions-item .txt-holder h3 {
  font-size: 24px !important;
  margin-bottom: 15px;
}

.open-positions-section .open-positions-item .txt-holder h5 {
  font-size: 16px !important;
}

.succes-stories-main-section,
.features-main-section {
  padding: 135px 0;
}

.succes-stories-main-section:nth-child(even),
.features-main-section:nth-child(even) {
  background-color: #F9FAFC;
}

.succes-stories-main-section .succes-stories-main-content,
.succes-stories-main-section .features-main-content,
.features-main-section .succes-stories-main-content,
.features-main-section .features-main-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.succes-stories-main-section .succes-stories-main-content .txt-holder,
.succes-stories-main-section .features-main-content .txt-holder,
.features-main-section .succes-stories-main-content .txt-holder,
.features-main-section .features-main-content .txt-holder {
  padding-right: 140px;
}

.succes-stories-main-section .succes-stories-main-content .txt-holder p.name,
.succes-stories-main-section .features-main-content .txt-holder p.name,
.features-main-section .succes-stories-main-content .txt-holder p.name,
.features-main-section .features-main-content .txt-holder p.name {
  margin-bottom: 0;
  color: #d5686c;
  font-weight: 700;
}

.succes-stories-main-section .succes-stories-main-content .img-holder,
.succes-stories-main-section .features-main-content .img-holder,
.features-main-section .succes-stories-main-content .img-holder,
.features-main-section .features-main-content .img-holder {
  position: relative;
}

.succes-stories-main-section .succes-stories-main-content .img-holder:hover .play-holder span::before,
.succes-stories-main-section .features-main-content .img-holder:hover .play-holder span::before,
.features-main-section .succes-stories-main-content .img-holder:hover .play-holder span::before,
.features-main-section .features-main-content .img-holder:hover .play-holder span::before {
  width: 150px;
  height: 150px;
}

.succes-stories-main-section .succes-stories-main-content .img-holder .play-holder,
.succes-stories-main-section .features-main-content .img-holder .play-holder,
.features-main-section .succes-stories-main-content .img-holder .play-holder,
.features-main-section .features-main-content .img-holder .play-holder {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 11;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.succes-stories-main-section .succes-stories-main-content .img-holder .play-holder span,
.succes-stories-main-section .features-main-content .img-holder .play-holder span,
.features-main-section .succes-stories-main-content .img-holder .play-holder span,
.features-main-section .features-main-content .img-holder .play-holder span {
  position: relative;
  display: block;
}

.succes-stories-main-section .succes-stories-main-content .img-holder .play-holder span::before,
.succes-stories-main-section .features-main-content .img-holder .play-holder span::before,
.features-main-section .succes-stories-main-content .img-holder .play-holder span::before,
.features-main-section .features-main-content .img-holder .play-holder span::before {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -1;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  content: "";
  background: #d5686c;
  width: 0;
  height: 0;
  border-radius: 50%;
  -webkit-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
  opacity: .1;
}

.succes-stories-main-section .succes-stories-main-content .img-holder img,
.succes-stories-main-section .features-main-content .img-holder img,
.features-main-section .succes-stories-main-content .img-holder img,
.features-main-section .features-main-content .img-holder img {
  border-radius: 20px 20px 0 0;
}

.features-page .features-main-section .features-main-content .img-holder img {
  border-radius: 20px;
}

.platform-section .platform-content .img-holder {
  position: relative;
}

.platform-section .platform-content .img-holder:hover .play-holder span::before {
  width: 150px;
  height: 150px;
}

.platform-section .platform-content .img-holder .play-holder {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 11;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.platform-section .platform-content .img-holder .play-holder span {
  position: relative;
  display: block;
}

.platform-section .platform-content .img-holder .play-holder span::before {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -1;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  content: "";
  background: #d5686c;
  width: 0;
  height: 0;
  border-radius: 50%;
  -webkit-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
  opacity: .1;
}

.platform-section .platform-content .img-holder img {
  border-radius: 20px 20px 0 0;
}

.homepage .bg-texture {
  background-position: top 100vh right;
}

.homepage .hero-section .hero-container .txt-holder {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  text-align: center;
  max-width: 80vw;
  width: 100%;
}

.homepage .hero-section .hero-container .txt-holder .img-holder {
  position: relative;
}

.homepage .hero-section .hero-container .txt-holder .img-holder:hover .play-holder span::before {
  width: 150px;
  height: 150px;
}

.homepage .hero-section .hero-container .txt-holder .img-holder .play-holder {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 11;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.homepage .hero-section .hero-container .txt-holder .img-holder .play-holder span {
  position: relative;
  display: block;
}

.homepage .hero-section .hero-container .txt-holder .img-holder .play-holder span::before {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -1;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  content: "";
  background: #d5686c;
  width: 0;
  height: 0;
  border-radius: 50%;
  -webkit-transition: all 0.25s ease-in-out 0s;
  transition: all 0.25s ease-in-out 0s;
  opacity: .1;
}
.homepage .homepage-main-section .homepage-main-item .col-6.txt-holder:not(.order-md-2) {
    padding-right: 100px;
}
.homepage .hero-section .hero-container .txt-holder .img-holder img {
  border-radius: 20px 20px 0 0;
}

.homepage .hero-section .hero-container .txt-holder .img-holder {
	display: inline-block;
    max-width: 500px;
}

.homepage .hero-section .hero-container .txt-holder .img-holder img {
  border-radius: 20px 20px 20px 20px;
}

.homepage .hero-section .hero-container .txt-holder h1 {
  font-weight: 700;
  margin-bottom: 30px;
  line-height: 1.2;
}

.homepage .hero-section .hero-container .txt-holder p {
  font-size: 24px;
  color: #fff;
}

.homepage .homepage-main-section {
  position: relative;
  padding-top: 150px;
}

.homepage .homepage-main-section::before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  content: "";
  z-index: -1;
  background: url(../images/bg-top-left.svg) no-repeat top left/contain;
}

.homepage .homepage-main-section .homepage-main-item {
  padding-bottom: 200px;
}

.homepage .homepage-main-section .homepage-main-item .txt-holder.order-md-2 {
  padding-left: 100px;
}

.pricing-feature-section {
  padding: 200px 0;
}

.pricing-feature-section .pricing-feature-content {
  position: relative;
  margin: 0 90px;
}

.pricing-feature-section .pricing-feature-content .pricing-feature-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-shadow: #EDF1F5 0px 50px 100px -20px, #EDF1F5 0px 30px 60px -30px, #EDF1F5 0px -2px 0px 0px inset;
          box-shadow: #EDF1F5 0px 50px 100px -20px, #EDF1F5 0px 30px 60px -30px, #EDF1F5 0px -2px 0px 0px inset;
  border-radius: 20px;
  background: #fff;
  padding: 50px;
  max-height: 175px;
}

.pricing-feature-section .pricing-feature-content .pricing-feature-item:first-of-type {
  width: calc(100% + 130px);
  z-index: 10;
  position: absolute;
  top: 0;
  left: 0;
}

.pricing-feature-section .pricing-feature-content .pricing-feature-item:last-of-type {
  border-radius: 0 20px 20px 0;
  padding-left: 160px;
  z-index: 9;
}

.pricing-feature-section .pricing-feature-content .pricing-feature-item .icon-holder {
  border-radius: 50%;
  background-color: #F6F9FB;
  width: 75px;
  min-width: 75px;
  height: 75px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-right: 45px;
}

.pricing-feature-section .pricing-feature-content .pricing-feature-item .txt-holder h3 {
  font-size: 24px !important;
  margin-bottom: 15px;
  font-weight: 700;
}

.pricing-feature-section .pricing-feature-content .pricing-feature-item .txt-holder p {
  font-size: 16px;
  margin-bottom: 0;
}

.homepage-succes-stories-section .homepage-succes-stories-container {
  padding: 30px 0;
}
.homepage-succes-stories-container .swiper {
	padding: 80px 0;
}
.homepage-succes-stories-section .homepage-succes-stories-container .txt-holder {
  padding-right: 140px;
}

.homepage-succes-stories-section .homepage-succes-stories-container .txt-holder p.name {
  margin-bottom: 0;
  color: #d5686c;
  font-weight: 700;
}

.homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item {
  background: #fff;
  margin-bottom: 100px;
  border-radius: 20px;
  -webkit-box-shadow: 0 50px 50px #EDF1F5;
          box-shadow: 0 50px 50px #EDF1F5;
  max-width: 1200px;
  margin: auto;
}
.swiper-pagination-bullet-active {
	opacity: 1;
	background: #d5686c !important;
}
.homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .img-holder {
  padding: 0;
}

.homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .img-holder img {
  border-radius: 20px 0 0 20px;
  margin-right: auto;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .img-holder.order-md-2 img {
  border-radius: 0 20px 20px 0;
  margin-left: auto;
}

.homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .txt-holder {
  padding-right: 90px;
  padding-left: 90px;
}

.homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .txt-holder p {
  font-size: 22px;
  font-weight: 300;
}

.homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .txt-holder p.name {
  font-size: 18px;
  font-weight: 700;
}

.homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .txt-holder p.title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 0;
}

.homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .txt-holder .icon-holder {
  margin-bottom: 40px;
}

.fancybox__backdrop {
  background: var(--fancybox-bg, rgba(255, 255, 255, 0.95)) !important;
}

.fancybox__content > .carousel__button.is-close {
  top: 30px !important;
  right: 50px !important;
}

.fancybox__carousel .fancybox__slide.has-video .fancybox__content,
.fancybox__carousel .fancybox__slide.has-html5video .fancybox__content {
  position: unset !important;
}

.fancybox__slide.has-caption.is-selected.has-video {
  position: relative !important;
}

.carousel__button svg {
  fill: unset !important;
  stroke: unset !important;
  stroke-width: unset !important;
  stroke-linejoin: unset !important;
  stroke-linecap: unset !important;
  -webkit-filter: unset !important;
          filter: unset !important;
}

.fancybox__caption {
  color: #d5686c !important;
  font-size: 24px !important;
  font-weight: 700 !important;
}

.fancybox__iframe,
.fancybox__content {
  border-radius: 20px !important;
}

.fancybox__content {
  -webkit-box-ordinal-group: 3 !important;
      -ms-flex-order: 2 !important;
          order: 2 !important;
}

.fancybox__content, .fancybox__caption {
  width: 960px !important;
  max-width: 100% !important;
}

.fancybox__slide {
  padding: 150px 160px !important;
}

.accordion-section {
  padding-bottom: 150px;
}

body.homepage main::after {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  z-index: -1;
  height: 100%;
  content: "";
  background: url(../images/bg-bottom-right.svg) no-repeat bottom right/contain;
}

body.features-page main::after {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  z-index: -1;
  height: 100%;
  content: "";
  background: url(../images/bg-bottom-right.svg) no-repeat bottom right/contain;
}

.bg-texture {
  background: url(../images/bg-texture.svg) no-repeat top right/contain;
}

.pricing-list-main-section .heading-holder {
  padding: 150px 0 100px 0;
}

.pricing-list-main-section .pricing-content {
  margin-right: -25px;
  margin-left: -25px;
}

.pricing-list-main-section .pricing-content .col-4 {
  padding-left: 25px;
  padding-right: 25px;
}

.pricing-list-main-section .pricing-content .pricing-item {
  border-radius: 20px;
  -webkit-box-shadow: #EDF1F5 0px 50px 100px -20px, #EDF1F5 0px 30px 60px -30px, #EDF1F5 0px -2px 0px 0px inset;
          box-shadow: #EDF1F5 0px 50px 100px -20px, #EDF1F5 0px 30px 60px -30px, #EDF1F5 0px -2px 0px 0px inset;
  text-align: center;
  padding: 50px;
  background-color: #fff;
}

.pricing-list-main-section .pricing-content .pricing-item h3 {
  font-weight: 700;
  font-size: 22px !important;
  margin-bottom: 14px;
}

.pricing-list-main-section .pricing-content .pricing-item h5 {
  font-weight: 700;
  font-size: 18px !important;
  margin-bottom: 50px;
  color: #d5686c;
}

.pricing-list-main-section .pricing-content .pricing-item .price-container {
  background: #F6F9FB;
  width: 140px;
  height: 140px;
  margin: auto;
  border-radius: 50%;
  margin-bottom: 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  position: relative;
  font-size: 36px;
  color: #d5686c;
  font-weight: 700;
  text-align: center;
}

.pricing-list-main-section .pricing-content .pricing-item .price-container span small {
  font-size: 14px;
}

.pricing-list-main-section .pricing-content .pricing-item .price-container .badge-holder {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  top: -10px;
  right: -80px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.pricing-list-main-section .pricing-content .pricing-item .price-container .badge-holder span {
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  margin-left: 8px;
}

.pricing-list-main-section .pricing-content .pricing-item ul {
  margin-bottom: 60px;
}

.pricing-list-main-section .pricing-content .pricing-item ul li {
  margin: 0;
  padding: 18px;
  border-bottom: 1px solid #E6EFF5;
  font-size: 18px;
  color: #95A9BA;
}

.pricing-list-main-section .pricing-content .pricing-item ul li:first-of-type {
  border-top: 1px solid #E6EFF5;
}

.pricing-list-main-section .pricing-content .pricing-item ul li strong {
  color: #617B91;
}

.benefits-section {
  padding-top: 200px;
}

.benefits-section .benefits-container .heading-holder {
  margin-bottom: 80px;
}

.benefits-section .benefits-container .benefits-content .benefits-item {
  margin: 0;
  padding: 24px;
  border-bottom: 1px solid #E6EFF5;
  font-size: 18px;
  color: #617B91;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.benefits-section .benefits-container .benefits-content .benefits-item:first-of-type {
  border-top: 1px solid #E6EFF5;
}

.benefits-section .benefits-container .benefits-content .benefits-item h3 {
  font-weight: 700;
  font-size: 18px !important;
  color: #d5686c;
}

.features-page .bg-texture {
  background-position: top 50vh right;
}

.features-page .features-offer-section .heading-holder {
  padding: 150px 0;
}

.features-page .features-offer-section .features-offer-item {
  margin-bottom: 200px;
}

.features-page .features-offer-section .features-offer-item:last-of-type {
  margin-bottom: 0;
}

.features-page .features-offer-section .features-offer-item .txt-holder.order-md-2 {
  padding-left: 100px;
}

.lever-section {
  padding: 200px 0;
}

.lever-section .heading-holder {
  margin-bottom: 150px;
}

.lever-section .lever-content {
  margin-left: -25px;
  margin-right: -25px;
}

.lever-section .lever-content .col-4 {
  padding-left: 25px;
  padding-right: 25px;
  margin-bottom: 50px;
}

.lever-section .lever-content .lever-item {
  padding: 60px 70px;
  border-radius: 20px;
  background-color: #fff;
  -webkit-box-shadow: #EDF1F5 0px 50px 100px -20px, #EDF1F5 0px 30px 60px -30px, #EDF1F5 0px -2px 0px 0px inset;
          box-shadow: #EDF1F5 0px 50px 100px -20px, #EDF1F5 0px 30px 60px -30px, #EDF1F5 0px -2px 0px 0px inset;
  height: 100%;
}

.lever-section .lever-content .lever-item .icon-holder {
  width: 75px;
  height: 75px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background: #d5686c;
  border-radius: 50%;
  margin-bottom: 30px;
  -webkit-box-shadow: 0 10px 20px rgba(218, 38, 46, 0.3);
          box-shadow: 0 10px 20px rgba(218, 38, 46, 0.3);
}
.lever-section .lever-content .lever-item .icon-holder img {
	width: 35px;
	height: 35px;
}
.lever-section .lever-content .lever-item .icon-holder.gray-bg {
  background: #F6F9FB !important;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.lever-section .lever-content .lever-item h3 {
  font-size: 24px !important;
  font-weight: 700;
  margin-bottom: 20px;
}

.lever-section .lever-content .lever-item p {
  font-size: 16px;
  color: #95A9BA;
}

.numbers-section {
  background-color: #F9FAFC;
  padding: 150px 0;
}

.numbers-section .heding-holder {
  margin-bottom: 200px;
}

.numbers-section .heding-holder h2 {
  font-weight: 700;
  font-size: 48px !important;
  line-height: 1.2;
}

.numbers-section .heding-holder p {
  font-size: 36px;
  font-weight: 300;
}

.numbers-section .numbers-content .numbers-item h3 {
  font-weight: 700;
  font-size: 64px !important;
  color: #d5686c;
}

.numbers-section .numbers-content .numbers-item p {
  font-size: 24px;
}

.about-us-page .bg-texture {
  background-position: top 50vh right;
  padding: 200px 0;
}

.about-us-page .features-main-section {
  padding-top: 0;
  padding-bottom: 330px;
}

.values-section .img-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.values-section .img-container .img-holder {
  position: relative;
  margin-right: 30px;
}

.values-section .img-container .img-holder .icon {
  position: absolute;
  top: 0;
  left: 50%;
  z-index: 10;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 118px;
}

.values-section .values-content .txt-holder {
  padding-left: 100px;
}

.answers-section .txt-holder .author {
  margin-left: 30px;
  color: #d5686c;
  font-size: 49px !important;
  font-weight: 500;
}

.answers-section .img-container .img-wrapper {
  text-align: center;
  position: relative;
}

.answers-section .img-container .img-wrapper .img-holder {
  background: #fff;
  display: inline-block;
  margin: auto auto 30px auto;
  text-align: center;
  -webkit-box-shadow: 0 20px 20px #EDF1F5;
          box-shadow: 0 20px 20px #EDF1F5;
  border-radius: 50%;
  padding: 0px 75px 52px 75px;
}

.answers-section .img-container .img-wrapper .img-holder img.author-img {
  border-radius: 50%;
  margin-bottom: 30px;
}

.answers-section .img-container .img-wrapper .heading-holder h3 {
  font-weight: 700;
  font-size: 24px !important;
  margin-bottom: 10px;
}

.answers-section .img-container .img-wrapper .heading-holder h5 {
  font-weight: 700;
  font-size: 18px !important;
  color: #d5686c;
}

.answers-section .img-container .img-wrapper .icon-holder {
  position: absolute;
  bottom: 50px;
  left: 75px;
}

.answers-section .img-container .img-wrapper .icon-holder img {
  height: 118px;
}
section + section {
    animation: load_section 0.8s forwards;
}
@keyframes load_section {
    0% {opacity: 0; transform: translateY(100px)}
    100% {opacity: 1; transform: translateY(0px)}
}
#search-form {
    display: none;
}
#search-container.active #search-btn {
    display: none;
}
#search-container.active #search-form {
    display: block !important;
}
#search-container input {
	box-shadow: none;
	border: 1px solid #E8EFF4;
	height: 50px;
	font-size: 16px;
}
.btn.link-btn:hover {
	background: rgba(0,0,0,0);
}
.pricing-feature-section .pricing-feature-content .pricing-feature-item .icon-holder img {
	height: 30px;
	object-fit: contain;
}
.w650 {
	max-width: 650px;
	margin: auto;
}
.w1000 {
	max-width: 1000px;
	margin: auto;
}
.features-subheader-section {
	padding: 100px 0 400px;
	background: #F9FAFC;
}
.features-subheader-section h1 {
	font-size: 48px !important;
	max-width: 650px;
	margin: 0 auto 45px;
	line-height: 60px;
}
.features-video-holder h3 {
	text-align: center;
	margin-bottom: 40px;
	font-size: 36px !important;
	font-weight: 700;
}
.features-video-holder .img-holder {
	padding: 0 20px;
}
[data-fancybox]:hover {
    cursor: pointer;
}
.features-main-section-new {
	margin-top: -400px;
	padding: 135px 0 0;
}
.features-main-text {
	width: 100%;
	max-width: 1000px;
	margin: auto;
	padding: 50px 15px 0;
}
.features-main-text h2 {
	font-weight: 700;
	text-align: center;
	margin-bottom: 40px;
}
.features-main-text h4 {
	font-size: 18px !important;
	color: #95A9BA;
	max-width: 700px;
	margin: 0 auto 90px;
	text-align: center;
}
.features-main-text p strong {
	font-weight: 700;
    color: #D5686C;
}
.features-main-text p:last-child {
    margin-bottom: 0;
}
.pricing-feature-item:hover {
	background: #fbfbfb !important;
}
.features-main-text p {
	font-size: 21px;
	line-height: 34px;
	margin-bottom: 40px;
	position: relative;
	padding-left: 30px;
}
.features-main-text p::before {
	content: "";
	position: absolute;
    top: 7px;
    left: -6px;
    background: url(../images/checked-icon.svg) no-repeat center top;
	width: 20px;
	height: 20px;
	display: block;
	z-index: 1;
}

.tabss {
	margin: 40px 0 70px 0;
	display: flex;
	align-items: center;
}
.custom-tabs .react-tabs__tab:focus::after {
	display: none;
}
.custom-tabs .react-tabs__tab {
	height: 47px;
	border-radius: 10px;
	background: #fff;
	box-shadow: none;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #617B91;
	font-size: 14px;
	font-weight: 700;
	padding: 0 25px;
	margin-right: 15px;
	white-space: nowrap;
	border: 1px solid #e8eff4;
	cursor: pointer;
}
.custom-tabs .react-tabs__tab-list {
	border-bottom: none;
	margin: 40px 0 70px 0;
	padding: 0;
	display: flex;
}
.custom-tabs .react-tabs__tab:hover,
.custom-tabs .react-tabs__tab--selected {
	background: #d5686c;
	box-shadow: 0px 20px 20px rgba(213, 104, 108, 0.2);
	color: #fff;
	border: 1px solid #d5686c;
}
.custom-tabs .react-tabs__tab-panel li {
    font-size: 18px;
	color: #95A9BA;
	line-height: 24px;
    margin-bottom: 15px;
    position: relative;
    padding-left: 20px;
}
.custom-tabs .react-tabs__tab-panel li::before {
    content: "";
    position: absolute;
    top: 8px;
    left: 0px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
	background: #d5686c;
}

.txt-editor-content ul {
	list-style: inherit;
	padding-left: 25px;
}
.values-section.platform-section {
	padding-top: 200px;
}
.hero-content .bg-holder img {
	width: 100% !important;
	max-width: 100% !important;
}
.hero-content .bg-holder > div {
	width: 100% !important;
	max-width: 100% !important;
}
.mobile-menu-button {
  position: absolute;
  top: 50%;
  right: 20px;
  z-index: 10;
  background: transparent;
  transform: translateY(-50%);
  display: none;
}
.thank-you-content {
  margin-right: 0;
  margin-left: 0;
}
.social-icons-content {
  position: fixed;
  top: 200px;
  right: 55px;
  z-index: 100;
}
.social-icons-item {
  width: 45px;
  height: 45px;
  background: #C2CDD8;
  border-radius: 50%;
  margin-bottom: 25px;
  transition: all .25s ease-in-out 0s;
}
.social-icons-item  a {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}
.social-icons-item:hover {
  background-color: #D5686C;
}
.social-icons-item:last-of-type {
  margin-bottom: 0;
}
.blog-full-offer-cta .img-holder img {
  border-radius: 20px 0 0 0;
}
.pricing-list-main-section {
	padding-top: 100px;
}
.sentences {
  display: flex;
  flex-direction: column;
}
.sentences h3 {
  padding: 15px 0;
  display: flex;
  align-items: center;
}
.sentences h3 .red-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  min-height: 50px;
  width: 50px;
  height: 50px;
  color: #fff;
  background: #d5686c;
  margin-right: 20px;
  border-radius: 50%;
}
.main-img-section .bg-holder > div, .main-img-section .bg-holder > div > div {
	max-width: none !important;
	width: 100% !important;
}
.main-img-section .bg-holder > div img {
	max-width: none !important;
	width: 100% !important;
    object-fit: cover !important;
}
.why-section {
	padding: 100px 0;
    -webkit-animation: load_section 0.8s forwards;
    animation: load_section 0.8s forwards;
}
.why-container h1 {
	font-size: 45px !important;
	color: #D8676E;
	margin-bottom: 30px;
}
.why-container .txt-editor-content p {
	margin-bottom: 0px;
	font-size: 19px;
	line-height: 33px;
}
.white-panel:last-child {
    margin-bottom: 0;
}
.white-panel {
	background: #FFFFFF;
	box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.06);
	border-radius: 40px;
	padding: 70px 70px 70px 40px;
	display: flex;
	margin-bottom: 100px;
}
.white-panel-content {
    font-weight: 400;
    font-size: 19px;
    line-height: 33px;
}
.white-panel-title {
	font-weight: 700;
	font-size: 29px !important;
	line-height: 33px;
	margin-bottom: 20px;
}
.white-panel-icon {
	width: 60px;
	height: 60px;
	background: #C76E6F;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	margin-right: 30px;
	border-radius: 50%;
	font-family: 'Lato';
	font-style: normal;
	font-weight: 700;
	font-size: 29px;
	line-height: 33px;
	color: #FFFFFF;
}
.blog-full-section .txt-editor-content {
	font-size: 20px;
}
.learn_categories > .news, .learn_categories > .articles {
	display: none;
}
.blog_categories > .news {
	display: none;
}
.testimonials-img img {
    max-width: 200px !important;
    max-height: 90px !important;
}
.swiper-tag {
	display: block;
	margin-bottom: 20px;
	color: #d5686c;
}
.testimonials-desc {
	position: relative;
	padding-bottom: 1px;
}
.homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .txt-holder .testimonials-desc p {
	max-height: 255px;
	overflow: hidden;
    transition: all 0.45s ease-in-out 0s;
}
.homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .txt-holder .testimonials-desc.active p {
	max-height: 1255px;
	overflow: hidden;
}
.read_more {
    position: absolute;
	bottom: 20px;
	right: 0;
	font-size: 14px;
    cursor: pointer;
}
.read_more:hover {
    color: #d5686c;
}
.grid {
    display: grid;
    grid-gap: 10px;
    grid-template-columns: repeat(auto-fill, minmax(250px,1fr));
    grid-auto-rows: 20px;
}
.bcorp-logo {
	position: relative;
	top: -30px;
	filter: invert();
}
.bcorp-logo a {
	position: relative;
	transform: translateX(20px);
	display: block;
}
.gp-logo {
	position: relative;
	top: -30px;
}
/* NEW PRICING */
.pricing-item.second-bg {
	padding: 38px 20px 18px 20px !important;
}
.pricing-list-main-section1 .pricing-content .pricing-item h3 {
	display: flex;
	align-items: center;
	margin: 0 !important;
	gap: 20px;
	font-size: 22px !important;
    font-weight: 500;
    text-align: left;
    line-height: 22px;
    height: 60px;
}
.pricing-list-main-section1 .pricing-content .pricing-item .required-box + h3 {
	padding-right: 25px;
}
.price-container-new {
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 30px;
	font-weight: 600;
}
.pricing-icon {
	width: 45px;
	height: 45px;
	min-width: 45px;
	min-height: 45px;
	max-width: 45px;
	max-height: 45px;
	background: #fff;
	display: flex;
	border-radius: 50%;
}
.fee-font {
	font-size: 16px;
	font-weight: 300;
}
.pricing-item.second-bg.gray-bg {
	background: #617b91 !important;
}
.pricing-item.second-bg.lightgray-bg {
	background: #95a9ba !important;
}
.pricing-item.second-bg.lightgray-bg2 {
	background: #dfdfe4 !important;
}
.container.pricing-list-main-container-new {
	max-width: 1720px;
    padding-left: 35px;
    padding-right: 35px;
}
.pricing-list-main-container-new .pricing-item {
    border-radius: 20px;
    height: 100% !important;
    position: relative;
}
.required-box {
	position: absolute;
	top: 4px;
	right: 4px;
	border-radius: 0 16px 0 0;
	width: 110px;
	height: 93px;
	overflow: hidden;
}
.required-box-text {
	background: #fff;
	width: 170px;
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	top: -5px;
	right: -61px;
	transform: rotate(45deg);
	padding-top: 18px;
	font-size: 12px;
	text-transform: uppercase;
}
.price-container-new {
	height: 110px;
	line-height: 1.2;
	flex-direction: column;
	align-items: flex-start;
 	justify-content: center;
}
.lightgray-bg2 hr {
	background: #9ba9b7;
}
.bottom-info {
	height: auto !important;
	font-size: 18px;
}
.bottom-info ul li {
	line-height: 1.2;
	padding-bottom: 12px;
	padding-left: 25px;
    position: relative;
}
.bottom-info ul li::before {
	content: "";
	position: absolute;
	top: 7px;
	left: 0px;
	width: 7px;
	height: 7px;
	border: 1px solid #617b91;
	border-radius: 50%;
}


/* responsive */
@media only screen and (max-width: 1680px) {
.price-container-new {
    font-size: 26px;
}
.pricing-content .col-3:nth-child(2) .white.fee-font {
    position: absolute;
    margin-top: 40px;
}
}
@media only screen and (max-width: 1440px) {
  .play-holder a img {
    height: 50px !important;
  }
  .homepage .hero-section .hero-container .txt-holder .img-holder > img {
    max-height: 20vh;
  }
  .homepage .hero-section .hero-container .txt-holder .img-holder:hover .play-holder span::before {
    width: 100px;
    height: 100px;
  }
  .hero-container h1 {
    font-size: 46px !important;
    line-height: 1.2;
    margin-bottom: 10px;
  }
  .homepage .hero-section .hero-container .txt-holder h1 {
    margin-bottom: 10px;
  }
  .homepage-succes-stories-section .homepage-succes-stories-container {
    padding: 100px 15px;
  }
  .price-container-new {
	font-size: 24px;
  }
  .pricing-content .col-3:nth-child(2) .white.fee-font {
    position: absolute;
    margin-top: 40px;
  }
}
@media only screen and (max-width: 1366px) {
  .blog-group-container .blog-group-content {
    margin-right: -15px;
    margin-left: -15px;
  }
  .filter-container .filter-content .filter-items {
    padding: 100px 15px;
  }
  .pricing-list-main-section .pricing-content .col-4 {
    padding-left: 15px;
    padding-right: 15px;
  }
  .pricing-list-main-section .pricing-content {
    margin-right: -15px;
    margin-left: -15px;
  }
  .img-section {
    padding: 100px 0;
  }
  .main-careers-section {
    padding: 100px 0;
  }
  .open-positions-section {
    padding: 100px 15px;
  }
  .homepage .homepage-main-section {
    padding-top: 100px;
  }
  .homepage .homepage-main-section .homepage-main-item {
    padding-bottom: 100px;
  }
  .pricing-feature-section {
    padding: 100px 0;
  }
  .homepage-succes-stories-section .homepage-succes-stories-container {
    padding: 50px 15px;
  }
  .homepage .pricing-feature-section {
    padding-top: 0;
  }
  .lever-section .lever-content {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 1200px) {
  header {
    padding: 0;
  }
  .accessibility-content .heading-content, .privacy-content .heading-content {
    padding: 50px 15px;
  }
  .social-icons-content {
    right: 20px;
  }
  .pricing-feature-section .pricing-feature-content .pricing-feature-item:last-of-type {
    padding-left: 120px;
  }
  .pricing-feature-section .pricing-feature-content .pricing-feature-item .icon-holder {
    margin-right: 15px;
  }
  h1 br,
  h2 br,
  h3 br,
  h4 br {
    display: none;
  }
  .numbers-section {
    padding: 100px 0;
  }
  .numbers-section .heding-holder {
    margin-bottom: 100px;
  }
  .about-us-page .bg-texture {
    padding: 100px 0;
  }
  .about-us-page .features-main-section {
    padding-bottom: 150px;
  }
  .lever-section {
    padding: 100px 0;
  }
  .mb-100 {
    margin-bottom: 50px !important;
  }
  .numbers-section .heding-holder h2 {
    font-size: 34px !important;
  }
  .numbers-section .heding-holder p {
    font-size: 26px;
  }
  .values-section .values-content .txt-holder {
    padding-left: 15px;
  }
  .answers-section .img-container .img-wrapper .icon-holder {
    left: 30px;
  }
}
@media only screen and (max-width: 1024px) {
  .link-item {
    margin-right: 20px;
  }
  .link-item .login-btn {
    padding: 13px 0 13px 20px;
  }
  .social-icons-content {
    position: unset;
    display: flex;
    justify-content: right;
    padding: 0 15px;
    margin-top: 30px;
  }
  .blog-full-section.mt-100 {
    margin-top: 0 !important;
  }
  .social-icons-item {
    margin-right: 15px;
    margin-bottom: 0px;
  }
  .blog-full-container {
	padding-left: 20px;
	padding-right: 20px;
  }
  .mb-100 {
    margin-bottom: 30px !important;
  }
  .txt-editor-content p {
    margin-bottom: 30px;
  }
  .txt-editor-content img {
    margin: 50px 0;
  }
  .txt-editor-content h3 {
    margin-bottom: 30px;
  }
  .blog-full-offer-section .blog-full-offer-heading {
    padding: 30px 0;
  }
  .mb-150 {
    margin-bottom: 50px !important;
  }
  .blog-full-container .blog-full-item {
    padding-bottom: 50px;
  }
  .filter-container .filter-content .filter-heading h2 {
    font-size: 36px !important;
  }
  .filter-container .filter-content .filter-items {
    padding: 30px 8px;
  }
  .blog-group-content a.col-4 {
    padding-left: 15px;
    padding-right: 15px;
    width: 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .pricing-list-main-section .heading-holder {
    padding: 50px 0 0 0;
  }
  .benefits-section {
    padding-top: 100px;
  }
  .pricing-list-main-section .pricing-content .pricing-item {
    padding: 30px;
  }
  .benefits-section .benefits-container .heading-holder {
    margin-bottom: 30px;
  }
  .pricing-feature-section {
    padding: 100px 0;
  }
  .pricing-feature-section .pricing-feature-content {
    margin: 0;
  }
  .pricing-feature-section .pricing-feature-content .pricing-feature-item:last-of-type {
    z-index: 9;
  }
  .homepage .hero-section .hero-container .txt-holder h1 {
    white-space: normal;
  }
  .homepage .hero-section .hero-container .txt-holder {
    width: 100%;
    padding: 0 15px;
  }
  .homepage .hero-section .bg-holder img {
    height: 70vh;
    object-fit: cover;
  }
  .homepage .homepage-main-section .homepage-main-item .txt-holder.order-md-2 {
    padding-left: 0;
  }
  .txt-holder h3 {
    font-size: 28px !important;
    margin-bottom: 20px;
  }
  .succes-stories-main-section .succes-stories-main-content .txt-holder, .succes-stories-main-section .features-main-content .txt-holder, .features-main-section .succes-stories-main-content .txt-holder, .features-main-section .features-main-content .txt-holder {
    padding-right: 15px;
  }
  .succes-stories-main-section, .features-main-section {
    padding: 50px 0;
  }
  .features-page .features-offer-section .heading-holder {
    padding: 50px 0;
  }
  .features-page .features-offer-section .features-offer-item .txt-holder.order-md-2 {
    padding-left: 15px;
  }
  .features-page .features-offer-section .features-offer-item {
    margin-bottom: 50px;
  }
  .pricing-feature-section {
    padding: 50px 0;
  }
  .lever-section {
    padding: 100px 0;
  }
  .lever-section .heading-holder {
    margin-bottom: 50px;
  }
  .lever-section .lever-content .col-4 {
    padding-left: 15px;
    padding-right: 15px;
    margin-bottom: 30px;
  }
  .lever-section .lever-content .lever-item {
    padding: 30px 30px;
  }
}
@media only screen and (max-width: 768px) {
    .pricing-list-main-container-new .col-3 {
        margin-bottom: 30px;
    }
    .f-48 {
        font-size: 38px !important;
    }
    .homepage .homepage-main-section .homepage-main-item .col-6.txt-holder:not(.order-md-2) {
        padding-right: 15px;
        order: 2;
    }
    body {
        overflow-x: hidden;
    }
    .pricing-content .btn.second-bg.white.box-shadow {
        font-size: 16px !important;
    }
    .accordion-content p {
        margin-bottom: 20px;
        font-size: 18px;
    }
    .homepage .hero-section .hero-container .txt-holder .img-holder .play-holder span {
        width: 70px;
        opacity: 0.9;
    }
  .swiper-slide .px-100 {
	padding-left: 0px !important;
	padding-right: 0px !important;
  }
  .homepage-succes-stories-container .swiper {
	padding: 0px 0 35px 0;
  }
  .read_more {
	bottom: 5px;
  }
  .homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .txt-holder .testimonials-desc p {
	max-height: 260px;
  }
  .model-tabs.react-tabs__tab {
	min-width: 80px;
    height: 40px;
  }
  body .fancybox__slide::after, .fancybox__slide::before {
	margin: 0 !important;
  }
  body .fancybox__slide {
    padding: 10px 20px !important;
    justify-content: center;
  }
  .fancybox__caption {
	font-size: 22px !important;
	line-height: 1.1 !important;
	margin-bottom: 15px !important;
  }
  .fancybox__content > .carousel__button.is-close {
	right: 20px !important;
  }
  .values-section.platform-section {
    padding-top: 100px;
  }
  footer .footer-cta-content {
    padding: 30px 0 30px 0;
  }
  .mobile-menu-button {
    display: block;
  }
  .menu-item-container {
    display: block !important;
    text-align: center;
    padding: 50px;
  }
  .menu-item {
    height: auto;
    max-height: 0;
    transition: all .5s ease-in-out 0s;
    overflow: hidden;
    position: absolute;
    top: 60px;
    left: 0;
    z-index: 100;
    background: #fff;
    width: 100%;
    max-width: 100%;
    box-shadow: 0px 10px 10px 0 rgba(0,0,0,0.05)
  }
  .menu-opened .menu-item {
    max-height: 1000px;
  }
  .mobile-menu-button .icon-bar {
    background-color: #d5686c;
  }
  .mobile-menu-button .icon-bar {
    display: block;
    width: 25px;
    height: 2px;
    border-radius: 5px;
    margin-bottom: 5px;
  }
  .mobile-menu-button .icon-bar:last-of-type {
    margin-bottom: 0;
  }
  .mobile-menu-button + .icon-bar {
    margin-top: 4px;
  }
  header .logo-holder img {
    height: 30px;
  }
  header {
    height: 60px;
  }
  body main {
    padding-top: 60px;
  }
  .link-item {
    margin-right: 0;
    margin-bottom: 20px;
  }
  .link-item .login-btn {
    padding: 0;
    display: inline-block;
    border: 0;
  }
  footer .footer-cta-content .footer-cta-txt h3 {
    font-size: 22px !important;
  }
  .footer-links-item {
    max-width: 50%;
    flex: 0 0 50%;
    margin-bottom: 30px;
  }
  footer .footer-links-content {
    padding: 50px 0;
  }
  .presale-cta h3 {
    font-size: 16px !important;
  }
  .all-right-txt p {
    font-size: 14px;
  }
  h1,
  .thank-you-container .txt-holder h1,
  .page-404-item .txt-holder h1 {
    font-size: 40px !important;
    white-space: normal;
    margin-bottom: 10px;
    line-height: 1.2;
  }
  .thank-you-container .txt-holder p,
  .page-404-item .txt-holder p {
    font-size: 20px !important;
    margin-bottom: 20px;
  }
  .page-404-item {
    padding: 20vh 15px;
  }
  .accessibility-content .heading-content p,
  .privacy-content .heading-content p {
    font-size: 20px;
  }
  .txt-editor-content,
  .accessibility-content .accessibility-item p,
  .accessibility-content .privacy-item p,
  .privacy-content .accessibility-item p,
  .privacy-content .privacy-item p {
    font-size: 18px;
    margin-bottom: 30px;
  }
  .accessibility-content .accessibility-item h3,
  .accessibility-content .privacy-item h3,
  .privacy-content .accessibility-item h3,
  .privacy-content .privacy-item h3 {
    margin-bottom: 30px;
    font-size: 30px !important;
  }
  .txt-editor-content h3 {
    font-size: 30px !important;
  }
  .accessibility-content .accessibility-item ul li,
  .accessibility-content .privacy-item ul li,
  .privacy-content .accessibility-item ul li,
  .privacy-content .privacy-item ul li {
    font-size: 18px;
  }
  .mb-150 {
    margin-bottom: 50px !important;
  }
  .contact-us-section {
    padding: 50px 15px;
  }
  .txt-holder.pr-100 {
    padding-right: 0 !important;
  }
  .contact-us-item {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 50px;
  }
  .help-center-main-section {
    padding: 50px 0;
  }
  .mb-80 {
    margin-bottom: 30px !important;
  }
  .help-center-cta-content .txt-holder {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 50px;
  }
  .help-center-cta-content .img-holder {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .txt-holder h3 {
    margin-bottom: 20px;
  }
  .help-center-cta-section {
    padding: 50px 0;
  }
  .txt-holder h3 {
    font-size: 30px !important;
  }
  .img-holder img {
    width: 100%;
  }
  .blog-full-container .heading-content {
    display: block;
  }
  .blog-full-container .heading-content h1 {
    font-size: 34px !important;
    margin-bottom: 30px;
  }
  .author-container .txt-holder p {
    margin-bottom: 20px;
  }
  .filter-content .col-6 {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    text-align: center;
  }
  .filter-container {
    margin-top: 30px;
  }
  .filter-container .filter-content .filter-items {
    justify-content: center;
  }
  .blog-group-container .blog-group-content .col-4 {
    padding-left: 15px;
    padding-right: 15px;
  }
  .succes-stories-main-section, .features-main-section {
    padding: 50px 0;
  }
  .succes-stories-main-section .succes-stories-main-content .txt-holder,
  .succes-stories-main-section .features-main-content .txt-holder,
  .features-main-section .succes-stories-main-content .txt-holder,
  .features-main-section .features-main-content .txt-holder {
    padding-right: 50px;
  }
  .pricing-content .col-4 {
    width: 50%;
    flex: 0 0 50%;
    max-width: 50%;
    margin-bottom: 30px;
  }
  .pricing-feature-section {
    padding: 50px 0;
  }
  .pricing-feature-content {
    display: block;
  }
  .pricing-feature-section .pricing-feature-content .pricing-feature-item {
    display: block;
    max-width: 100%;
    margin-bottom: 30px;
  }
  .pricing-feature-section .pricing-feature-content .pricing-feature-item:first-of-type {
    width: 100%;
    position: unset;
  }
  .pricing-feature-section .pricing-feature-content .pricing-feature-item:last-of-type {
    padding: 50px;
    margin-left: 0;
    width: 100%;
    border-radius: 20px;
  }
  .main-careers-section {
    padding: 50px 0;
  }
  .main-careers-section .txt-holder {
    padding-right: 0;
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 50px;
  }
  .main-careers-content .img-holder {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    padding-left: 70px;
  }
  .main-careers-content .img-holder .icon-holder {
    left: 70px;
  }
  .img-section {
    padding: 50px 0;
  }
  .open-positions-section {
    padding: 50px 15px;
  }
  .homepage-main-item .txt-holder,
  .homepage-main-item .img-holder  {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0 15px;
    margin-bottom: 30px;
  }
  .homepage .homepage-main-section .homepage-main-item .txt-holder.order-md-2 {
    padding-left: 15px;
    padding: 0 15px;
  }
  .homepage .homepage-main-section .homepage-main-item {
    padding-bottom: 50px;
  }
  .homepage .homepage-main-section {
    padding-top: 50px;
  }
  .homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item {
    padding: 0 15px;
  }
  .homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .txt-holder {
    padding-right: 30px;
    padding-left: 30px;
  }
  .homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .txt-holder p {
    font-size: 18px;
    font-weight: 300;
    margin-bottom: 20px;
  }
  .homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .txt-holder .icon-holder {
    margin-bottom: 15px;
  }
  .homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item {
    background: #fff;
    margin-bottom: 50px;
  }
  .succes-stories-main-section .succes-stories-main-content .txt-holder, .succes-stories-main-section .features-main-content .txt-holder, .features-main-section .succes-stories-main-content .txt-holder, .features-main-section .features-main-content .txt-holder {
    padding-right: 15px;
  }
  .features-main-content .txt-holder,
  .features-main-content .img-holder {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 30px;
  }
  .features-page .features-offer-section .features-offer-item {
    display: block;
  }
  .features-offer-item .txt-holder,
  .features-offer-item .img-holder {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 30px;
  }
  .feature-content .txt-holder,
  .feature-content .img-holder {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 30px;
  }
  .lever-content .col-4 {
    width: 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .heding-holder .col-5,
  .heding-holder .col-7 {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 30px;
  }
  .numbers-section {
    padding: 50px 0;
  }
  .numbers-section .heding-holder {
    margin-bottom: 50px;
  }
  .numbers-section .numbers-content .numbers-item h3 {
    font-size: 40px !important;
  }
  .values-content .txt-holder,
  .values-content .img-holder {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 30px;
  }
  .answers-content .img-container,
  .answers-content .txt-holder {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 30px;
  }
  .about-us-page .features-main-section {
    padding-bottom: 100px;
  }
  .answers-section .img-container .img-wrapper .icon-holder {
    left: 110px;
  }
}
@media only screen and (max-width: 767px) {
    .jcc-mob {
        justify-content: center !important;
    }
    .logo-holder {
        display: inline-block;
        position: relative;
        top: 3px;
    }
  .container, .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }
  footer .footer-cta-content {
    text-align: center;
  }
  footer .footer-cta-content .all-right-txt {
    text-align: center;
  }
  .footer-cta-content .jcr {
    justify-content: center !important;
  }
  .footer-cta-txt {
    margin-bottom: 20px;
  }
  .thank-you-container .txt-holder {
    width: 100%;
    padding: 0 15px;
  }
  .thank-you-container {
    padding-left: 0;
    padding-right: 0;
  }
  .success-stories-page .img-section .img-container .img-content img {
    margin: auto;
  }
  .success-stories-page .img-section .img-container .img-content .col-4 {
    margin-bottom: 30px;
    margin-top: 0 !important;
  }
  .open-positions-item .txt-holder p {
    margin-bottom: 0;
  }
  .open-positions-section .open-positions-item {
    padding: 30px;
  }
  .open-positions-section .open-positions-item .txt-holder h3 {
    font-size: 18px !important;
    margin-bottom: 10px;
    padding-right: 5px;
  }
  .homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item {
    display: block;
  }
  .homepage .homepage-main-section .homepage-main-item {
    display: flex;
  }
  .pricing-feature-section .pricing-feature-content .pricing-feature-item {
	box-shadow: #EDF1F5 0px 50px 100px 0px, #EDF1F5 0px 30px 60px 10px, #EDF1F5 0px -2px 0px 0px inset;
  }
  .homepage-succes-stories-section .homepage-succes-stories-container .homepage-succes-stories-item .txt-holder {
    padding: 30px;
  }
  .numbers-content .numbers-item {
    text-align: center;
  }
  .lever-content .col-4 {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .values-content .img-holder img {
    width: auto;
  }
  .values-content .img-container {
    flex: 0 0 100%;
  }
  .values-section .img-container .img-holder {
    margin-right: 0;
  }
  .custom-tabs .react-tabs__tab {
	height: 32px;
	font-size: 10px;
	padding: 0 3px;
	margin-right: 10px;
  }
  .custom-tabs .react-tabs__tab-list {
	margin: 40px 0 40px;
	padding: 0;
	flex-wrap: wrap;
  }
  .why-section {
	padding: 40px 15px;
	-webkit-animation: load_section 0.8s forwards;
	animation: load_section 0.8s forwards;
  }
  .why-container h1 {
	font-size: 30px !important;
  }
  .why-container .txt-editor-content p {
	font-size: 18px;
	line-height: 28px;
  }
  .pb-150 {
    padding-bottom: 50px !important;
  }
  .pt-150 {
    padding-top: 50px !important;
  }
  .sections-section.pt-150.pb-150.lightGray-bg.pt-mob-3 {
	padding-left: 10px;
	padding-right: 10px;
  }
  .white-panel {
	display: flex;
	flex-direction: column;
    padding: 30px;
  }
  .white-panel-icon {
	margin-bottom: 20px;
  }
  .white-panel-content {
	font-weight: 400;
	font-size: 18px;
	line-height: 28px;
  }
  .white-panel-title {
	font-weight: 700;
	font-size: 24px !important;
	line-height: 30px;
	margin-bottom: 20px;
  }

}
@media only screen and (max-width: 576px) {
  .main-img-section .main-img-container .main-img-content .main-img-item .heading-holder {
    left: 50%;
    transform: translate(-50%, 0%);
    width: 100%;
    text-align: center;
  }
  .main-img-section .main-img-container .main-img-content .main-img-item {
	height: 160px;
  }
  .blog-group-content a.col-4 {
    padding-left: 15px;
    padding-right: 15px;
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .pricing-content .col-4 {
    width: 100%;
    flex: 0 0 100%;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
  .answers-section .img-container .img-wrapper .icon-holder {
    left: 3vw;
  }
  .values-section .img-container .img-holder {
    margin-right: 15px;
  }
  .fancybox__slide::before, .fancybox__slide::after {
    margin: 0;
  }
  .fancybox__slide {
    padding: 100px 30px !important;
  }
  .fancybox__content {
    height: 190px !important;
  }
}
@media only screen and (max-width: 480px) {
  .footer-links-item {
    max-width: 100%;
    flex: 0 0 100%;
    text-align: center;
  }
  .filter-container .filter-content .filter-items {
    flex-wrap: wrap;
  }

  .bg-holder img {
	min-height: 160px !important;
  }
  .link-item .link {
      line-height: 30px;
  }
  .link-item > ul > li {
      margin-bottom: 20px;
  }
  .social-icons-content {
	position: relative;
	justify-content: center;
	right: auto;
	width: 100%;
	top: 0;
	flex-direction: revert;
	margin: 20px 0 0 !important;
}

  .link-item > ul {
	position: relative;
	top: auto;
	left: auto;
    margin-top: -60px;
	background: #fff;
	border: none;
	padding: 10px 20px;
	pointer-events: auto;
	opacity: 1;
	visibility: visible;
  }
  .hero-item .img-fluid {
	height: 100% !important;
  }
  footer .footer-links-content .social-icons-holder {
    justify-content: center;
  }
  .footer-links-item .logo-holder a img {
    margin: auto;
  }
  footer .footer-links-content {
    padding: 30px 0;
  }
  .thank-you-container .txt-holder h1 {
    font-size: 30px !important;
  }
  .footer-links-item:last-of-type {
    margin-bottom: 0;
  }
  .txt-holder h3,
  .accessibility-content .accessibility-item h3,
  .accessibility-content .privacy-item h3,
  .privacy-content .accessibility-item h3,
  .privacy-content .privacy-item h3 {
    font-size: 24px !important;
  }
  .btn {
    padding: 13px 15px;
    font-size: 12px;
  }
  .filter-container .filter-content .filter-items .filter-item {
    margin-right: 10px;
  }
  .filter-container .filter-content .filter-items .filter-item .btn.search-btn {
    padding: 16px 24px !important;
  }
  .filter-container .filter-content .filter-heading h2 {
    font-size: 28px !important;
  }
  .txt-holder p:last-of-type {
    margin-bottom: 40px;
  }
  .pricing-feature-section .pricing-feature-content .pricing-feature-item:last-of-type {
    padding: 30px;
  }
  .pricing-feature-section .pricing-feature-content .pricing-feature-item {
    padding: 30px;
  }
  .lever-content .col-4 {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .numbers-section .heding-holder h2 {
    font-size: 24px !important;
  }
  .numbers-section .heding-holder p {
    font-size: 20px;
  }
}
@media only screen and (max-width: 400px) {
  footer .footer-cta-content .footer-cta-btn .btn {
    left: 10px;
  }
  .bg-holder img {
    min-height: 10vh;
    object-fit: cover;
  }
  .thank-you-item .bg-holder img {
    min-height: 50vh;
  }
  h1, .thank-you-container .txt-holder h1, .page-404-item .txt-holder h1 {
    font-size: 35px !important;
  }
  .txt-holder h5 {
    font-size: 14px !important;
    margin-bottom: 10px;
  }
  .blog-group-container .blog-group-content .blog-group-item .txt-holder {
    padding: 30px;
  }
  .filter-container .filter-content .filter-items .filter-item {
    margin-right: 5px;
  }
  .pricing-feature-section .pricing-feature-content .pricing-feature-item {
    padding: 30px;
    max-height: 1175px;
  }
  .pricing-feature-section .pricing-feature-content .pricing-feature-item:last-of-type {
    padding: 30px;
  }
  .open-positions-section .open-positions-item {
    display: block;
  }
  .open-positions-item .txt-holder {
    margin-bottom: 15px;
  }
  .hero-container h1 {
    font-size: 34px !important;
  }
  .answers-section .txt-holder .author {
    font-size: 30px !important;
  }
  .about-us-page .bg-texture {
    padding: 50px 0;
  }
  .answers-section .img-container .img-wrapper .icon-holder img {
    height: 70px;
  }
  .lever-section {
    padding: 50px 0;
  }
  .features-subheader-section h1 {
	font-size: 28px !important;
	max-width: 650px;
	margin: 0 auto 25px;
	line-height: 36px;
  }
  .txt-holder p:last-of-type {
	margin-bottom: 40px;
	font-size: 18px !important;
  }
  .features-video-holder h3 {
    margin-bottom: 20px;
    font-size: 26px !important;
  }
  .features-main-section-new {
    margin-top: 0;
    padding: 135px 0 0;
  }
  .features-subheader-section {
    padding: 70px 0 70px;
    background: #f9fafc;
  }
  .features-main-text {
    padding: 50px 15px 0;
  }
  .features-main-text p {
    font-size: 20px;
    line-height: 32px;
    margin-bottom: 40px;
  }
  .pricing-feature-section .pricing-feature-content .pricing-feature-item .icon-holder {
	margin-right: 15px;
	margin-bottom: 20px;
  }
  .features-page .features-offer-section .features-offer-item {
	display: flex;
  }
  .order-mob-2 {
	order: 2;
  }
  .features-video-holder:first-child {
	margin-bottom: 50px;
  }
  .hero-section {
	height: 70vh;
  }
  .hero-section .bg-holder > div > div {
	padding-top: 70vh !important;
  }
}
@media only screen and (max-width: 320px) {
  .btn {
    padding: 13px 10px;
    font-size: 10px;
  }
  .badge-holder img {
    max-width: 30px;
    display: block;
  }
  .pricing-list-main-section .pricing-content .pricing-item .price-container .badge-holder {
    top: 10px;
    right: -60px;
  }
}
