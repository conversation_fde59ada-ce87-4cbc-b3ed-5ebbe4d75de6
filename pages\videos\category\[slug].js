import React from "react"
import Learncard from "../../../components/learncard"
import { fetchAPI } from "../../../lib/api"
import Layout from "../../../components/layout"
import Seo from "../../../components/seo"
import Categories from "../../../components/categories"
import Search from "../../../components/search"
import Link from "next/link"
import Image from "next/image"
import Script from "next/script"
import { getStrapiMedia } from "../../../lib/media"

const Category = ({ category, categories }) => {
  const seo = {
    metaTitle: category.attributes.name,
    metaDescription: `All ${category.attributes.name} posts`,
  }

  return (
    <Layout>
    <Seo seo={seo} />
          <main>
              <section className="main-img-section">
                  <div className="container-fluid main-img-container">
                      <div className="row main-img-content">
                          <div className="col-12 main-img-item">
                              <div className="bg-holder">
                                  <Image src={getStrapiMedia(category.attributes.image)} alt="Video" className="img-fluid" width={category.attributes.image.data.attributes.width} height={category.attributes.image.data.attributes.height} />
                                  {category.attributes.id}
                              </div>
                              <div className="heading-holder">
                                  <div className="container">
                                      <h1>{category.attributes.name}</h1>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              </section>
              <section className="main-blog-section">
                  <div className="container filter-container mb-100">
                      <div className="row filter-content">
                          <div className="col-6 filter-heading">
                              <h2>Read. Get Inspired.</h2>
                          </div>
                          <ul className="col-6 filter-items">
                              <li className="filter-item">
                                  <Link href={"/videos"}><a className="btn border-btn">ALL</a></Link>
                              </li>
                              {(categories.data).map((cat, i) => {
                                  return (
                                      <Categories
                                          category={cat}
                                          current={category.attributes.slug}
                                          type={'videos'}
                                          key={`learn_single_category__${cat.attributes.id}`}
                                      />
                                  )
                              })}
                              <Search type={'learn'} />
                          </ul>
                      </div>
                  </div>
                  <div className="container blog-group-container mb-150">
                      <div className="row blog-group-content w100">
                          {(category.attributes.learns.data).map((post, i) => {
                              return (
                                  <Learncard
                                      post={post}
                                      key={`learn_posts__${i}`}
                                  />
                              )
                          })}
                      </div>
                  </div>
              </section>
          </main>
        <Script async src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js" />
    </Layout>
  )
}

export async function getStaticPaths() {
    try {
        const categoriesRes = await fetchAPI("/categories", { fields: ["slug"] })
        
        return {
            paths: categoriesRes.data.map((category) => ({
                params: {
                    slug: category.attributes.slug,
                },
            })),
            fallback: false,
        }
    } catch (error) {
        console.error("Error in getStaticPaths:", error)
        return {
            paths: [],
            fallback: false
        }
    }
}

export async function getStaticProps({ params }) {
    try {
        const matchingCategories = await fetchAPI("/categories", {
            filters: {
                slug: {
                    $eq: params.slug
                }
            },
            populate: {
                image: {
                    populate: "*"
                },
                learns: {
                    populate: "*",
                    sort: 'createdAt:desc'
                },
            },
        })
        const allCategories = await fetchAPI("/categories", {populate: "*"})

        return {
            props: {
                category: matchingCategories.data[0],
                categories: allCategories,
            },
            revalidate: 1,
        }
    } catch (error) {
        console.error("Error in getStaticProps:", error)
        return {
            notFound: true
        }
    }
}

export default Category
