import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import { fetchAPI } from "../lib/api"
import Story from "../components/story"
import Link from "next/link"
import Image from "next/image"
import { getStrapiMedia } from "../lib/media"

const Stories = ({ pages, stories }) => {
    return (
        <Layout>
            <Seo seo={pages.attributes.seo} />
            <main>
                <section className="main-img-section">
                    <div className="container-fluid main-img-container">
                        <div className="row main-img-content">
                            <div className="col-12 main-img-item">
                                <div className="bg-holder">
                                    <Image src="/images/main-img.jpg" width="1920" height="300" alt="Ethos" className="img-fluid" />
                                </div>
                                <div className="heading-holder">
                                    <div className="container">
                                        <h1>{pages.attributes.title}</h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                {stories.map((story, i) => {
                    return (
                        <section className="succes-stories-main-section"  key={`group_story_key_${i}`}>
                            <div className="container succes-stories-main-container">
                                <div className="row succes-stories-main-content">
                                    <div className="col-6 txt-holder">
                                        <h3>{story.attributes.title}</h3>
                                        <p>{story.attributes.description}</p>
                                        <p className="name">{story.attributes.fullname}</p>
                                        <p className="title mb-0">{story.attributes.company}</p>
                                    </div>
                                    <div className="col-6 img-holder">
                                        <div className="icon-holder">
                                            <Image src="/images/circle-icon.svg" width={60} height={60} alt="ethos circle" className="img-fluid" />
                                        </div>
                                        <Image src={getStrapiMedia(story.attributes.image)} width={story.attributes.image.data.attributes.width} height={story.attributes.image.data.attributes.height} className="img-fluid" alt="ethos success story" />
                                    </div>
                                </div>
                            </div>
                        </section>
                    )
                })}
            </main>
        </Layout>
    )
}

export async function getStaticProps() {
    // Run API calls in parallel
    const storiesRes = await fetchAPI("/stories", { populate: "*" })
    const pagesRes = await fetchAPI("/pages/11", {
        populate: {
            sections: {
                populate: {
                    image: "*"
                }
            },
            seo: {
                populate: "*"
            }
        }
    })

    return {
        props: {
            stories: storiesRes.data,
            pages: pagesRes.data
        },
        revalidate: 1,
    }
}

export default Stories
