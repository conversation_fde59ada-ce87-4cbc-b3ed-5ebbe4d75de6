import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import { fetchAPI } from "../lib/api"
import Image from 'next/image'
import Link from 'next/link'
import { getStrapiMedia } from "../lib/media"

const Pricing = ({ pages, plans }) => {
  return (
    <Layout pages={pages}>
        <Seo seo={pages.attributes.seo} />
        <main>
            <section className="main-img-section">
                <div className="container-fluid main-img-container">
                    <div className="row main-img-content">
                        <div className="col-12 main-img-item">
                            <div className="bg-holder">
                                <Image src={getStrapiMedia(pages.attributes.headerimage)} width="1920" height="300" alt="Ethos" className="img-fluid" />
                            </div>
                            <div className="heading-holder">
                                <div className="container">
                                    <h1>{pages.attributes.title}</h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <div className="bg-texture">
                <section className="pricing-list-main-section1 pt-2 mt-4">
                    <div className="container pricing-list-main-container-new">
                        <div className="row py-5">
                            <div className="col-12 txt-holder text-center heading-holder">
                                <h1 className="red f-48 mb-2">Get Started Today! </h1>
                            </div>
                        </div>
                        <div className="row pricing-content">
                            <div className="col-3">
                                <div className="pricing-item second-bg">
                                    <span className="required-box"><span className="required-box-text">required</span></span>
                                    <h3 className="white"><span className="pricing-icon"></span>Annual License</h3>
                                    <hr className="mb-0" />
                                    <div className="price-container-new">
                                        <span className="white">Starting at $12,000</span>
                                        <span className="white fee-font">annual fee</span>
                                    </div>
                                    <hr className="mt-0" />
                                    <p className="white text-left bottom-info"><ul>
                                            <li>Unlimited logins.</li>
                                            <li>Unlimited use of the tool.</li>
                                        </ul></p>
                                </div>
                            </div>
                            <div className="col-3">
                                <div className="pricing-item second-bg gray-bg">
                                    <span className="required-box"><span className="required-box-text">required</span></span>
                                    <h3 className="white"><span className="pricing-icon"></span>Concierge Onboarding</h3>
                                    <hr className="mb-0" />
                                    <div className="price-container-new">
                                        <span className="white">$10,000 - $50,000+</span>
                                        <span className="white fee-font">one-time fee</span>
                                    </div>
                                    <hr className="mt-0" />
                                    <p className="white text-left bottom-info">
                                        <ul>
                                            <li>We hold your hand every step of the way as you get onboarded onto the system.</li>
                                        </ul>
                                    </p>
                                </div>
                            </div>
                            <div className="col-3">
                                <div className="pricing-item second-bg lightgray-bg">
                                    <h3 className="white"><span className="pricing-icon"></span>Social Impact Partnership Package</h3>
                                    <hr className="mb-0" />
                                    <div className="price-container-new">
                                        <span className="white text-left">Quarterly/Monthly</span>
                                        <span className="white fee-font">custom pricing</span>
                                    </div>
                                    <hr className="mt-0" />
                                    <p className="white text-left bottom-info">
                                        <ul>
                                            <li>Your &quot;accountant&quot; for social impact, supporting data collection, measurement, and reporting.</li>
                                        </ul>
                                    </p>
                                </div>
                            </div>
                            <div className="col-3">
                                <div className="pricing-item second-bg lightgray-bg2">
                                    <h3 className=""><span className="pricing-icon"></span>Communication Support</h3>
                                    <hr className="mb-0" />
                                    <div className="price-container-new">
                                        <span className="">Ad Hoc</span>
                                        <span className="fee-font">custom pricing</span>
                                        {/* <span className="white fee-font">one-time fee</span> */}
                                    </div>
                                    <hr className="mt-0" />
                                    <p className="text-left bottom-info">
                                        <ul>
                                            <li>CSR, Social Impact, and ESG reports.</li>
                                            <li>Writing and designing white papers.</li>
                                            <li>Investor decks.</li>
                                        </ul>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="row pricing-content">
                            <div className="col-12">
                                <div className="text-center py-5">
                                    <h4 className="mb-5 f-24">To set up an Ethos Tracking demo, or for additional questions please reach out.</h4>
                                    <Link href="/contact-us"><a className="btn second-bg white box-shadow" style={{padding: "20px 40px", fontSize: "20px"}}>CONTACT US TO GET STARTED</a></Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section className="mt-5 pt-5">
                    <div className="container benefits-container mw-900">
                        {(pages.attributes.sections).map((section, i) => {
                            return (
                                <div className="row" key={'single_sect_title_' + i}>
                                    <div className="col-12 heading-holder txt-holder mb-0">
                                        {/* <h5>{section.smalltitle}</h5> */}
                                        {/* <h3 className="text-center">{section.title}</h3> */}
                                        {/* <p>{section.description}</p> */}
                                        <div className="row features-main-content">
                                            <div className="features-main-text">
                                                <div dangerouslySetInnerHTML={{ __html: pages.attributes.content }} />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )
                        })}
                        {/* <ul className="row benefits-content">
                            {(pages.attributes.features).map((feature, i) => {
                                if(feature.isTitle){
                                    return (
                                        <li className="col-12 benefits-item">
                                            <h3>{feature.title}</h3>
                                        </li>
                                    )
                                }else{
                                    return (
                                        <li className="col-12 benefits-item">
                                            <p>{feature.title}</p>
                                            <Image src="/images/checked-icon.svg" alt="Checked" width={20} height={20} className="img-fluid" />
                                        </li>
                                    )
                                }
                            })}
                        </ul> */}
                    </div>
                </section>

                <section className="pricing-feature-section">
                    <div className="container pricing-feature-container">
                        <div className="row pricing-feature-content">
                            <Link href="features">
                                <a className="col-6 pricing-feature-item">
                                    <div className="icon-holder">
                                        <Image src="/images/save-money-icon.svg" alt="Pricing" width={31} height={31} className="img-fluid" />
                                    </div>
                                    <div className="txt-holder">
                                        <h3>View Features →</h3>
                                        {/* <p>Aliquam interdum risus vitae lectus convallis sodales<br /> quis dignissim mi.</p> */}
                                    </div>
                                </a>
                            </Link>
                            <Link href="contact-us">
                                <a className="col-12 pricing-feature-item offset-5 col-7">
                                    <div className="icon-holder">
                                        <Image src="/images/question-icon.svg" alt="Pricing" width={17} height={30} className="img-fluid" />
                                    </div>
                                    <div className="txt-holder">
                                        <h3>Contact Sales →</h3>
                                        {/* <p>Aliquam interdum risus vitae lectus convallis sodales<br /> quis dignissim mi.</p> */}
                                    </div>
                                </a>
                            </Link>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </Layout>
  )
}


export async function getServerSideProps() {
    try {
        const pagesRes = await fetchAPI("/pages/12", {
            populate: {
                sections: {
                    populate: {
                        image: "*"
                    }
                },
                features: {
                    populate: {
                        image: "*"
                    }
                },
                image: {
                    populate: "*"
                },
                headerimage: {
                    populate: "*"
                },
                seo: {
                    populate: "*"
                }
            }
        })
        const plansRes = await fetchAPI("/pricing-plans", {
            populate: {
                list: "*"
            }
        })

        return {
            props: {
                plans: plansRes.data || [],
                pages: pagesRes.data || null,
            }
        }
    } catch (error) {
        console.error("Error fetching pricing data:", error);
        return {
            props: {
                plans: [],
                pages: null,
            }
        }
    }
}

export default Pricing
