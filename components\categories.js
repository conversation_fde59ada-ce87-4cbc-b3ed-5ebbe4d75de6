import React from "react"
import <PERSON> from "next/link"

const Categories = ({ category, current, type }) => {
  return (
    <Link href={`/${type}/category/${category.attributes.slug}`} passHref>
        <li className={"filter-item " + category.attributes.slug}>
            <a className={" btn border-btn text-uppercase" + (current == category.attributes.slug ? 'active second-bg box-shadow white' : '') + ""}>{category.attributes.name}</a>
        </li>
    </Link>
  )
}

export default Categories
