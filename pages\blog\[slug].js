import ReactMarkdown from "react-markdown"
import Moment from "react-moment"
import { fetchAPI } from "../../lib/api"
import { getStrapiMedia } from "../../lib/media"
import Layout from "../../components/layout"
import Seo from "../../components/seo"
import Image from "next/image"
import Link from "next/link"
import { FacebookShareButton, LinkedinShareButton, TwitterShareButton, EmailShareButton } from 'next-share'
import { useRouter } from 'next/router'
import getConfig from 'next/config'

const Post = ({ post }) => {
  // Add safety checks for post data
  if (!post || !post.attributes) {
    return (
      <Layout>
        <main>
          <section className="blog-full-section pt-5">
            <div className="container mw-1000 blog-full-container">
              <div className="row blog-full-content">
                <div className="col-12 heading-content mb-100">
                  <h1>Post not found</h1>
                </div>
                <div className="col-12 blog-full-item">
                  <div className="btn-holder">
                    <Link href="/blog"><a className="btn border-btn fs-14">BACK TO BLOG</a></Link>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
      </Layout>
    );
  }

  const seo = {
    metaTitle: post.attributes.title,
    metaDescription: post.attributes.description,
    shareImage: post.attributes.image,
    post: true,
  }
  const { asPath, pathname } = useRouter();
  const { publicRuntimeConfig } = getConfig();

  const copyText = (entryText) => {
    navigator.clipboard.writeText(entryText);
    app_msg('URL copied');
    // alert('COPIED!');
  }
  
  // Get author data safely
  const author = post.attributes.author?.data || { attributes: { name: "Ethos Team", picture: null } };
  
  return (
    <Layout>
        <Seo seo={seo} />
        <main>
            <ul className="social-icons-content">
                <li className="social-icons-item">
                    <FacebookShareButton url={ publicRuntimeConfig.baseUrl + asPath } quote={post.attributes.title} hashtag={'#ethostracking'}>
                        <span>
                            <Image src="/images/fb-share-icon.svg" alt="Facebook" className="img-fluid" width="11" height="23" />
                        </span>
                    </FacebookShareButton>
                </li>
                <li className="social-icons-item">
                    <LinkedinShareButton url={ publicRuntimeConfig.baseUrl + asPath } title={post.attributes.title} >
                        <span>
                            <Image src="/images/in-share-icon.svg" alt="Facebook" className="img-fluid" width="11" height="23" />
                        </span>
                    </LinkedinShareButton>
                </li>
                <li className="social-icons-item">
                    <TwitterShareButton url={ publicRuntimeConfig.baseUrl + asPath } title={post.attributes.title} >
                        <span>
                            <Image src="/images/tw-share-icon.svg" alt="Facebook" className="img-fluid" width="11" height="23" />
                        </span>
                    </TwitterShareButton>
                </li>
                <li className="social-icons-item">
                    <EmailShareButton url={ publicRuntimeConfig.baseUrl + asPath } subject={'Ethos Tracking new post: ' + post.attributes.title} >
                        <span>
                            <Image src="/images/email-share-icon.svg" alt="Facebook" className="img-fluid" width="11" height="23" body={ publicRuntimeConfig.baseUrl + asPath }/>
                        </span>
                    </EmailShareButton>
                </li>
                <li className="social-icons-item">
                    <span onClick={() => copyText(publicRuntimeConfig.baseUrl + asPath)}><Image src="/images/copy-share-icon.svg" alt="Copy" className="img-fluid" width="11" height="23" /></span>
                </li>
            </ul>

            <section className="blog-full-section pt-5">
                <div className="container mw-1000 blog-full-container">
                    <div className="row blog-full-content">
                            {post.attributes.image?.data && (
                                <div className="col-12 main-img-content mb-100">
                                    <div style={{display: "inline-block", maxWidth: "100%", overflow: "hidden", position: "relative", boxSizing: "border-box", margin: 0}}>
                                        <div style={{boxSizing: "border-box", display: "block", maxWidth: "100%"}}>
                                            <img 
                                                alt="" 
                                                aria-hidden="true" 
                                                src={`data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iJHtwb3N0LmF0dHJpYnV0ZXMuaW1hZ2UuZGF0YS5hdHRyaWJ1dGVzLndpZHRofSIgaGVpZ2h0PSIke3Bvc3QuYXR0cmlidXRlcy5pbWFnZS5kYXRhLmF0dHJpYnV0ZXMuaGVpZ2h0fSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2ZXJzaW9uPSIxLjEiLz4=`} 
                                                style={{maxWidth: "100%", display: "block", margin: 0, border: "none", padding: 0}} 
                                            />
                                        </div>
                                        <Image 
                                            src={getStrapiMedia(post.attributes.image)} 
                                            alt={post.attributes.title} 
                                            width={post.attributes.image.data.attributes.width} 
                                            height={post.attributes.image.data.attributes.height} 
                                            className="img-fluid w100" 
                                            style={{
                                                position: "absolute", 
                                                inset: 0, 
                                                boxSizing: "border-box", 
                                                padding: 0, 
                                                border: "none", 
                                                margin: "auto", 
                                                display: "block", 
                                                width: 0, 
                                                height: 0, 
                                                minWidth: "100%", 
                                                maxWidth: "100%", 
                                                minHeight: "100%", 
                                                maxHeight: "100%"
                                            }}
                                        />
                                    </div>
                                </div>
                            )}
                        <div className="col-12 heading-content mb-100">
                            <h1>{post.attributes.title}</h1>
                            <div className="author-container">
                                <div className="img-holder">
                                    {author.attributes.picture?.data && (
                                        <div style={{display: "inline-block", maxWidth: "100%", overflow: "hidden", position: "relative", boxSizing: "border-box", margin: 0}}>
                                            <div style={{boxSizing: "border-box", display: "block", maxWidth: "100%"}}>
                                                <img alt="" aria-hidden="true" src={`data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmVyc2lvbj0iMS4xIi8+`} style={{maxWidth: "100%", display: "block", margin: 0, border: "none", padding: 0}} />
                                            </div>
                                            <Image 
                                                src={getStrapiMedia(author.attributes.picture)} 
                                                alt={author.attributes.name || "Author"} 
                                                width={60} 
                                                height={60} 
                                                style={{
                                                    position: "absolute", 
                                                    inset: 0, 
                                                    boxSizing: "border-box", 
                                                    padding: 0, 
                                                    border: "none", 
                                                    margin: "auto", 
                                                    display: "block", 
                                                    width: 0, 
                                                    height: 0, 
                                                    minWidth: "100%", 
                                                    maxWidth: "100%", 
                                                    minHeight: "100%", 
                                                    maxHeight: "100%"
                                                }}
                                            />
                                        </div>
                                    )}
                                </div>
                                <div className="txt-holder">
                                    <strong>By: {author.attributes.name || "Ethos Team"}</strong>
                                    <p><Moment format="MMM DD, YYYY.">{post.attributes.publishedAt}</Moment></p>
                                </div>
                            </div>
                        </div>
                        <div className="col-12 blog-full-item">
                            <div className="txt-editor-content">
                                <div className="blog-content" dangerouslySetInnerHTML={{__html: post.attributes.content}} />
                            </div>
                            <div className="btn-holder">
                                <Link href="/blog" className="btn border-btn fs-14">BACK TO BLOG</Link>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* <section className="blog-full-offer-section mb-150">
                <div className="container mw-1000 blog-full-offer-container">
                    <div className="row blog-full-offer-heading">
                        <div className="col-12 txt-holder text-center">
                            <h5>WHAT WE OFFER</h5>
                            <h3>Supercharge Impact Tracking, Management, and Reporting</h3>
                            <p>Aliquam interdum risus vitae lectus convallis sodales quis dignissim mi. Proin sem risus, aliquet in pretium eu, ultrices eu nibh. Sed eu metus mollis, fringilla ligula quis.</p>
                        </div>
                    </div>
                    <div className="row blog-full-offer-cta">
                        <div className="col-6 txt-holder">
                            <h3>Get Started Now!</h3>
                            <p>Aliquam interdum risus vitae lectus convallis sodales.</p>
                            <a className="btn second-bg white box-shadow">GET STARTED</a>
                        </div>
                        <div className="col-6 img-holder">
                            <Image src="/images/offer-img.jpg" alt="Emily Kane Miller" className="img-fluid" width="450" height="301" />
                        </div>
                    </div>
                </div>
            </section> */}
        </main>
    </Layout>
  )
}

export async function getStaticPaths() {
  try {
    const postsRes = await fetchAPI("/posts", {
      pagination: {
        page: 0,
        pageSize: 100
      },
      fields: ["slug"]
    });

    return {
      paths: postsRes.data.map((post) => ({
        params: {
          slug: post.attributes.slug,
        }
      })),
      fallback: 'blocking' // Change from false to 'blocking' to handle new posts
    };
  } catch (error) {
    console.error("Error generating static paths:", error);
    return {
      paths: [],
      fallback: 'blocking'
    };
  }
}

export async function getStaticProps({ params }) {
  try {
    const postsRes = await fetchAPI("/posts", {
      filters: {
        slug: params.slug,
      },
      populate: {
        image: "*",
        author: {
          populate: "*"
        },
        category: {
          populate: "*"
        }
      },
    });
    
    // Safety check - if no data or empty data, return fallback
    if (!postsRes || !postsRes.data || postsRes.data.length === 0) {
      return {
        notFound: true, // This will show a 404 page
      };
    }

    return {
      props: {
        post: postsRes.data[0],
      },
      revalidate: 1,
    };
  } catch (error) {
    console.error("Error fetching post data:", error);
    return {
      notFound: true, // This will show a 404 page
    };
  }
}

export default Post
