import React from "react"
import Link from "next/link"
import Image from "next/image"

const Nav = () => {
  const menu_button = () => {
    document.querySelector("body").classList.toggle("menu-opened");
    document.querySelector(".mobile-menu-button").classList.toggle("active");
  }
  const link_click = () => {
    document.querySelector("body").classList.remove("menu-opened");
    document.querySelector(".mobile-menu-button").classList.remove("active");
  }
  return (
    <div>
        <header className="w100">
            <div className="container">
                <div className="row flex aic">
                    <span className="mobile-menu-button" onClick={menu_button}>
                        <span className="icon-bar"></span>
                        <span className="icon-bar"></span>
                        <span className="icon-bar"></span>
                    </span>
                    <div className="col logo-item">
                        <Link href="/"><a className="logo-holder"><Image src="/images/logo.svg" alt="ethos logo" width="130" height="50" className="img-fluid" /></a></Link>
                    </div>
                    <div className="col ml-auto menu-item">
                        <ul className="flex aic jcr menu-item-container">
                            <li className="link-item" onClick={link_click}>
                                <Link href={"/"}><a className="link" title="home">home</a></Link>
                            </li>
                            <li className="link-item" onClick={link_click}>
                                <Link href={"/about-us"}><a className="link" title="about">about</a></Link>
                            </li>
                            <li className="link-item" onClick={link_click}>
                                <Link href={"/features"}><a className="link" title="features">features</a></Link>
                            </li>
                            <li className="link-item" onClick={link_click}>
                                <Link href={"/pricing"}><a className="link" title="pricing">pricing</a></Link>
                            </li>
                            <li className="link-item" onClick={link_click}>
                                <span className="link" title="resources">resources</span>
                                <ul>
                                    <li><Link href={"/blog"}><a className="link" title="blog">Blog</a></Link></li>
                                    <li><Link href={"/videos"}><a className="link" title="videos">Videos</a></Link></li>
                                </ul>
                            </li>
                            <li className="link-item" onClick={link_click}>
                                <Link href={"/contact-us"}><a className="link" title="contact sales">contact sales</a></Link>
                            </li>
                            {/* <li className="link-item" onClick={link_click}>
                                <a className="link login-btn" title="" data-popup="login-popup">login</a>
                            </li>
                            <li className="link-item" onClick={link_click}>
                                <Link href={"/contact-us"}><a className="btn second-bg white text-uppercase" title="get started">get started</a></Link>
                            </li> */}
                        </ul>
                    </div>
                </div>
            </div>
        </header>
    </div>
  )
}

export default Nav

{/* <nav className="uk-navbar-container" data-uk-navbar>
        <div className="uk-navbar-left">
          <ul className="uk-navbar-nav">
            <li>
              <Link href="/">
                <a>Strapi Blog</a>
              </Link>
            </li>
          </ul>
        </div>
        <div className="uk-navbar-right">
          <ul className="uk-navbar-nav">
            {categories.map((category) => {
              return (
                <li key={category.id}>
                  <Link href={`/category/${category.attributes.slug}`}>
                    <a className="uk-link-reset">{category.attributes.name}</a>
                  </Link>
                </li>
              )
            })}
          </ul>
        </div>
      </nav> */}
